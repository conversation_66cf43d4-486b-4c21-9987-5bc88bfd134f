{% extends 'base.html' %}

{% block title %}修改密码 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
{% if current_user.is_authenticated and current_user.username is defined %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endif %}
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">修改密码</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('auth.change_password') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="mb-3">
                            <label for="old_password" class="form-label">原密码</label>
                            <input type="password" class="form-control" id="old_password" name="old_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">新密码</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">确认新密码</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div id="password-feedback" class="invalid-feedback"></div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> 返回
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg"></i> 保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const newPasswordInput = document.getElementById('new_password');
        const confirmPasswordInput = document.getElementById('confirm_password');
        const passwordFeedback = document.getElementById('password-feedback');

        function validatePasswords() {
            if (confirmPasswordInput.value && newPasswordInput.value !== confirmPasswordInput.value) {
                confirmPasswordInput.classList.add('is-invalid');
                passwordFeedback.textContent = '两次输入的密码不一致';
                return false;
            } else {
                confirmPasswordInput.classList.remove('is-invalid');
                passwordFeedback.textContent = '';
                return true;
            }
        }

        newPasswordInput.addEventListener('input', validatePasswords);
        confirmPasswordInput.addEventListener('input', validatePasswords);

        document.querySelector('form').addEventListener('submit', function(e) {
            if (!validatePasswords()) {
                e.preventDefault();
            }
        });
    });
</script>
{% endblock %}
