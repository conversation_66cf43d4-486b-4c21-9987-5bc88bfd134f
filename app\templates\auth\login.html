{% extends "base.html" %}

{% block title %}登录 - 图书借阅管理系统{% endblock %}

{% block login_content %}
<div class="login-card animate__animated animate__fadeIn">
    <div class="login-header">
        <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="图书借阅管理系统" class="login-logo">
        <h1 class="login-title">图书借阅管理系统</h1>
        <p class="login-subtitle">欢迎使用图书借阅管理系统，请登录您的账号</p>
    </div>

    <div class="login-body">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                        <i class="bi bi-info-circle me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('auth.login') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div class="mb-4">
                <div class="btn-group w-100" role="group" aria-label="用户类型选择">
                    <input type="radio" class="btn-check" name="user_type" id="reader" value="reader" autocomplete="off" checked>
                    <label class="btn btn-outline-primary" for="reader">
                        <i class="bi bi-person-badge me-2"></i>读者登录
                    </label>

                    <input type="radio" class="btn-check" name="user_type" id="staff" value="staff" autocomplete="off">
                    <label class="btn btn-outline-primary" for="staff">
                        <i class="bi bi-person-workspace me-2"></i>工作人员登录
                    </label>

                    <input type="radio" class="btn-check" name="user_type" id="admin" value="admin" autocomplete="off">
                    <label class="btn btn-outline-primary" for="admin">
                        <i class="bi bi-shield-lock me-2"></i>管理员登录
                    </label>
                </div>
            </div>

            <div class="mb-3">
                <label for="username" class="form-label">
                    <i class="bi bi-person-vcard me-1"></i>读者编号
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-person"></i>
                    </span>
                    <input type="text" class="form-control" id="username" name="username" placeholder="请输入读者编号" required>
                </div>
            </div>

            <div class="mb-4">
                <label for="password" class="form-label">
                    <i class="bi bi-key-fill me-1"></i>密码
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-lock"></i>
                    </span>
                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        <i class="bi bi-clock-history me-1"></i>记住我
                    </label>
                </div>
                <a href="#" class="text-decoration-none">
                    <i class="bi bi-question-circle me-1"></i>忘记密码？
                </a>
            </div>

            <button type="submit" class="btn btn-primary w-100 py-2">
                <i class="bi bi-box-arrow-in-right me-2"></i>登录系统
            </button>
        </form>
    </div>

    <div class="login-footer">
        <p class="mb-0">© {{ now.year }} 图书借阅管理系统 版权所有</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 根据用户类型切换提示文本
    document.addEventListener('DOMContentLoaded', function() {
        const userTypeInputs = document.querySelectorAll('input[name="user_type"]');
        const usernameInput = document.getElementById('username');
        const usernameLabel = document.querySelector('label[for="username"]');

        userTypeInputs.forEach(input => {
            input.addEventListener('change', function() {
                if (this.value === 'reader') {
                    usernameInput.placeholder = '请输入读者编号';
                    usernameLabel.innerHTML = '<i class="bi bi-person-vcard me-1"></i>读者编号';
                } else if (this.value === 'staff') {
                    usernameInput.placeholder = '请输入工作证号';
                    usernameLabel.innerHTML = '<i class="bi bi-person-workspace me-1"></i>工作证号';
                } else {
                    usernameInput.placeholder = '请输入管理员账号';
                    usernameLabel.innerHTML = '<i class="bi bi-person-badge-fill me-1"></i>管理员账号';
                }
            });
        });
    });
</script>
{% endblock %}
