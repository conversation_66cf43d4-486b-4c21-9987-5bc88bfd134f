#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库工具
"""

from flask import abort

from .. import db


def init_db():
    """初始化数据库"""
    db.create_all()


def get_or_404(model, id, message=None):
    """获取记录或返回404错误"""
    record = model.query.get(id)
    if record is None:
        abort(404, description=message or f'{model.__name__} 记录不存在')
    return record


def commit_changes():
    """提交数据库更改"""
    try:
        db.session.commit()
        return True
    except Exception as e:
        db.session.rollback()
        raise e
