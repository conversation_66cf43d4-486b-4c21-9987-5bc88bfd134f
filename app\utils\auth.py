#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
认证工具
"""

from functools import wraps
from flask import flash, redirect, url_for
from flask_login import current_user


def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))

        # 检查是否为管理员（通过用户ID前缀判断）
        if not current_user.get_id().startswith('admin_'):
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('auth.login'))

        return f(*args, **kwargs)
    return decorated_function


def reader_required(f):
    """读者权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))

        # 检查是否为读者（通过用户ID前缀判断，非admin_和staff_前缀即为读者）
        user_id = current_user.get_id()
        if user_id.startswith('admin_') or user_id.startswith('staff_'):
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('auth.login'))

        return f(*args, **kwargs)
    return decorated_function


def staff_required(f):
    """工作人员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))

        # 检查是否为工作人员（通过用户ID前缀判断）
        if not current_user.get_id().startswith('staff_'):
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('auth.login'))

        # 检查工作人员是否为活跃状态
        if hasattr(current_user, 'is_active_staff') and not current_user.is_active_staff():
            flash('您的账户已被停用，请联系管理员', 'danger')
            return redirect(url_for('auth.login'))

        return f(*args, **kwargs)
    return decorated_function


def staff_or_admin_required(f):
    """工作人员或管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))

        # 检查是否为工作人员或管理员
        user_id = current_user.get_id()
        if not (user_id.startswith('admin_') or user_id.startswith('staff_')):
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('auth.login'))

        # 如果是工作人员，检查是否为活跃状态
        if user_id.startswith('staff_'):
            if hasattr(current_user, 'is_active_staff') and not current_user.is_active_staff():
                flash('您的账户已被停用，请联系管理员', 'danger')
                return redirect(url_for('auth.login'))

        return f(*args, **kwargs)
    return decorated_function
