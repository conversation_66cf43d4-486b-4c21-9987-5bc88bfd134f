{% extends 'base.html' %}

{% block title %}借书处理 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
<!-- Tom Select CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/tom-select/tom-select.bootstrap5.min.css') }}">
<!-- 备用CSS路径 -->
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/tom-select/tom-select.min.css') }}">
<style>
    /* 自定义Tom Select样式 */
    .ts-wrapper.single .ts-control {
        background: linear-gradient(to right, rgba(26, 58, 95, 0.05), rgba(66, 153, 225, 0.05));
        border-color: #e2e8f0;
        border-radius: var(--input-border-radius);
        padding: 0.5rem 0.75rem;
        min-height: 42px;
        font-size: 1rem;
    }
    .ts-wrapper.focus .ts-control {
        border-color: #4299e1;
        box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
    }
    .ts-dropdown {
        border-radius: 0.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid #e2e8f0;
    }
    .ts-dropdown .option {
        padding: 10px 15px;
    }
    .ts-dropdown .active {
        background-color: rgba(66, 153, 225, 0.1);
        color: #1a3a5f;
    }
    .ts-dropdown .create {
        color: #4299e1;
    }
    .ts-dropdown .optgroup-header {
        background: #f8f9fa;
        color: #718096;
        font-weight: bold;
        border-bottom: 1px solid #e2e8f0;
    }
    .ts-wrapper.multi .ts-control > div {
        background: linear-gradient(135deg, #1a3a5f, #4299e1);
        color: #fff;
        border-radius: 3px;
        border: none;
    }
</style>
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 借书处理卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">借书处理</h6>
                    <a href="{{ url_for('admin.loans') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('admin.create_loan') }}" method="post">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="mb-4">
                            <label for="reader_id" class="form-label">选择读者 <span class="text-danger">*</span></label>
                            <select class="form-select" id="reader_id" name="reader_id" required placeholder="输入学号或姓名搜索读者...">
                                <option value="">请选择读者</option>
                                {% for reader in readers %}
                                <option value="{{ reader.reader_id }}" data-max-books="{{ reader.reader_type_info.max_books }}"
                                        data-reader-id="{{ reader.reader_id }}" data-name="{{ reader.name }}" data-department="{{ reader.department }}">
                                    {{ reader.name }} ({{ reader.reader_id }}) - {{ reader.department }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                <span id="reader-info">请选择读者以查看借阅信息</span>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="isbn" class="form-label">选择图书 <span class="text-danger">*</span></label>
                            <select class="form-select" id="isbn" name="isbn" required placeholder="输入书名、ISBN或作者搜索图书...">
                                <option value="">请选择图书</option>
                                {% for book in books %}
                                <option value="{{ book.isbn }}" data-available="{{ book.available_quantity }}"
                                        data-isbn="{{ book.isbn }}" data-title="{{ book.title }}" data-author="{{ book.author }}">
                                    {{ book.title }} ({{ book.isbn }}) - {{ book.author }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                <span id="book-info">请选择图书以查看可借数量</span>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="alert alert-info" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                <span id="loan-info">请选择读者和图书以查看借阅信息</span>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary" id="reset-btn">
                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                            </button>
                            <button type="submit" class="btn btn-primary" id="submit-btn">
                                <i class="bi bi-journal-plus"></i> 确认借书
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Tom Select JS -->
<script src="{{ url_for('static', filename='vendor/tom-select/tom-select.complete.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化读者选择器
        const readerSelect = new TomSelect('#reader_id', {
            plugins: ['clear_button'],
            maxItems: 1,
            valueField: 'value',
            labelField: 'text',
            searchField: ['text'],
            create: false,
            score: function(search) {
                const scoringFunction = this.getScoreFunction(search);
                return function(item) {
                    // 提高学号和姓名匹配的权重
                    if (item.text.indexOf(search) !== -1) {
                        return 2;
                    }
                    return scoringFunction(item);
                };
            },
            render: {
                option: function(data, escape) {
                    // 从选项文本中提取信息
                    const text = data.text;
                    const parts = text.split(' ');
                    const name = parts[0];
                    const readerId = parts[1] ? parts[1].replace(/[()]/g, '') : '';
                    const department = parts.length > 2 ? parts.slice(2).join(' ').replace(/^- /, '') : '';

                    return `<div class="d-flex align-items-center py-1">
                        <div>
                            <div class="mb-1">
                                <span class="h6">${escape(name)}</span>
                                <span class="badge bg-primary ms-2">${escape(readerId)}</span>
                            </div>
                            <div class="text-muted small">${escape(department)}</div>
                        </div>
                    </div>`;
                },
                item: function(data, escape) {
                    const text = data.text;
                    const parts = text.split(' ');
                    const name = parts[0];
                    const readerId = parts[1] ? parts[1].replace(/[()]/g, '') : '';

                    return `<div>${escape(name)} (${escape(readerId)})</div>`;
                }
            },
            dropdownParent: 'body',
            controlInput: '<input>',
            placeholder: '输入学号或姓名搜索读者...'
        });

        // 初始化图书选择器
        const bookSelect = new TomSelect('#isbn', {
            plugins: ['clear_button'],
            maxItems: 1,
            valueField: 'value',
            labelField: 'text',
            searchField: ['text'],
            create: false,
            score: function(search) {
                const scoringFunction = this.getScoreFunction(search);
                return function(item) {
                    // 提高书名、ISBN和作者匹配的权重
                    if (item.text.indexOf(search) !== -1) {
                        return 2;
                    }
                    return scoringFunction(item);
                };
            },
            render: {
                option: function(data, escape) {
                    // 从选项文本中提取信息
                    const text = data.text;
                    const parts = text.split(' - ');
                    const titleParts = parts[0].split(' ');
                    const title = titleParts.slice(0, -1).join(' ');
                    const isbn = titleParts[titleParts.length - 1].replace(/[()]/g, '');
                    const author = parts.length > 1 ? parts[1] : '';

                    return `<div class="d-flex align-items-center py-1">
                        <div>
                            <div class="mb-1">
                                <span class="h6">${escape(title)}</span>
                            </div>
                            <div class="text-muted small">
                                <span class="me-2"><i class="bi bi-upc me-1"></i>${escape(isbn)}</span>
                                <span><i class="bi bi-person me-1"></i>${escape(author)}</span>
                            </div>
                        </div>
                    </div>`;
                },
                item: function(data, escape) {
                    const text = data.text;
                    const parts = text.split(' - ');
                    const titleParts = parts[0].split(' ');
                    const title = titleParts.slice(0, -1).join(' ');
                    const isbn = titleParts[titleParts.length - 1].replace(/[()]/g, '');

                    return `<div>${escape(title)} (${escape(isbn)})</div>`;
                }
            },
            dropdownParent: 'body',
            controlInput: '<input>',
            placeholder: '输入书名、ISBN或作者搜索图书...'
        });

        const readerInfo = document.getElementById('reader-info');
        const bookInfo = document.getElementById('book-info');
        const loanInfo = document.getElementById('loan-info');
        const submitBtn = document.getElementById('submit-btn');
        const resetBtn = document.getElementById('reset-btn');

        // 初始禁用提交按钮
        submitBtn.disabled = true;

        // 读者选择变更事件
        readerSelect.on('change', function() {
            updateLoanInfo();

            if (this.getValue()) {
                // 获取选中的原始选项元素
                const originalSelect = document.getElementById('reader_id');
                const selectedOption = originalSelect.querySelector(`option[value="${this.getValue()}"]`);
                const maxBooks = selectedOption.getAttribute('data-max-books');
                readerInfo.innerHTML = `最大可借数量: <strong>${maxBooks}</strong> 本`;
            } else {
                readerInfo.innerHTML = '请选择读者以查看借阅信息';
            }
        });

        // 图书选择变更事件
        bookSelect.on('change', function() {
            updateLoanInfo();

            if (this.getValue()) {
                // 获取选中的原始选项元素
                const originalSelect = document.getElementById('isbn');
                const selectedOption = originalSelect.querySelector(`option[value="${this.getValue()}"]`);
                const available = selectedOption.getAttribute('data-available');
                bookInfo.innerHTML = `可借数量: <strong>${available}</strong> 本`;
            } else {
                bookInfo.innerHTML = '请选择图书以查看可借数量';
            }
        });

        // 更新借阅信息
        function updateLoanInfo() {
            const readerId = readerSelect.getValue();
            const isbn = bookSelect.getValue();

            if (readerId && isbn) {
                // 获取选中的原始选项元素
                const readerOriginalSelect = document.getElementById('reader_id');
                const bookOriginalSelect = document.getElementById('isbn');
                const readerOption = readerOriginalSelect.querySelector(`option[value="${readerId}"]`);
                const bookOption = bookOriginalSelect.querySelector(`option[value="${isbn}"]`);

                // 从选项文本中提取名称和标题
                const readerText = readerOption.textContent;
                const bookText = bookOption.textContent;
                const readerName = readerText.split(' ')[0];
                const bookTitle = bookText.split(' (')[0];

                loanInfo.innerHTML = `读者 <strong>${readerName}</strong> 将借阅图书 <strong>${bookTitle}</strong>`;

                // 启用提交按钮
                submitBtn.disabled = false;
            } else {
                loanInfo.innerHTML = '请选择读者和图书以查看借阅信息';

                // 禁用提交按钮
                submitBtn.disabled = true;
            }
        }

        // 重置按钮事件
        resetBtn.addEventListener('click', function() {
            readerSelect.clear();
            bookSelect.clear();
            readerInfo.innerHTML = '请选择读者以查看借阅信息';
            bookInfo.innerHTML = '请选择图书以查看可借数量';
            loanInfo.innerHTML = '请选择读者和图书以查看借阅信息';
            submitBtn.disabled = true;
        });

        // 表单验证
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            const readerId = readerSelect.getValue();
            const isbn = bookSelect.getValue();

            if (!readerId || !isbn) {
                alert('请选择读者和图书');
                event.preventDefault();
                return;
            }
        });
    });
</script>
{% endblock %}
