# 图书借阅管理系统用户功能说明文档

## 目录

- [1. 系统概述](#1-系统概述)
- [2. 通用功能](#2-通用功能)
  - [2.1 用户登录](#21-用户登录)
  - [2.2 修改密码](#22-修改密码)
- [3. 管理员功能](#3-管理员功能)
  - [3.1 仪表盘](#31-仪表盘)
  - [3.2 读者管理](#32-读者管理)
  - [3.3 图书管理](#33-图书管理)
  - [3.4 借阅管理](#34-借阅管理)
  - [3.5 统计分析](#35-统计分析)
- [4. 读者功能](#4-读者功能)
  - [4.1 个人仪表盘](#41-个人仪表盘)
  - [4.2 图书检索](#42-图书检索)
  - [4.3 借阅记录](#43-借阅记录)
  - [4.4 个人信息](#44-个人信息)

## 1. 系统概述

图书借阅管理系统是一个基于Web的应用程序，旨在为图书馆提供全面的图书管理和借阅服务。系统支持两种用户角色：管理员和读者，分别具有不同的功能权限。

- **管理员**：负责系统管理和日常业务处理，包括读者管理、图书管理、借阅管理和统计分析等功能。
- **读者**：图书馆用户，可以检索图书、借阅图书、归还图书、续借图书和查看个人借阅记录等。

## 2. 通用功能

### 2.1 用户登录

**功能描述**：用户通过输入账号和密码登录系统，系统根据用户角色（管理员或读者）跳转到相应的功能页面。

**访问路径**：系统首页 `/` 或 `/login`

**操作步骤**：
1. 在浏览器中访问系统首页
2. 在登录页面输入账号和密码
3. 点击"登录"按钮提交登录信息
4. 系统验证账号和密码，成功后跳转到相应的功能页面

**输入参数**：
- 账号：文本字段，必填
- 密码：密码字段，必填

**输出结果**：
- 登录成功：跳转到相应的功能页面（管理员跳转到管理员仪表盘，读者跳转到读者仪表盘）
- 登录失败：显示错误提示信息

**业务规则**：
- 管理员账号格式：字母和数字组合
- 读者账号格式：通常为学号或工号

**可能的错误情况**：
- 账号不存在：显示"账号不存在"错误提示
- 密码错误：显示"密码错误"错误提示
- 账号被禁用：显示"账号已被禁用，请联系管理员"错误提示

**界面描述**：
登录页面采用左右分栏布局，左侧（65%宽度）显示系统背景图，右侧（35%宽度）为登录表单。登录表单包含系统标题、账号输入框、密码输入框和登录按钮。

**登录流程图**：

```mermaid
flowchart TD
    A[访问系统首页] --> B[输入账号和密码]
    B --> C[点击登录按钮]
    C --> D{验证账号和密码}
    D -->|验证成功| E{判断用户角色}
    D -->|验证失败| F[显示错误提示]
    F --> B
    E -->|管理员| G[跳转到管理员仪表盘]
    E -->|读者| H[跳转到读者仪表盘]
```

### 2.2 修改密码

**功能描述**：用户可以修改自己的登录密码。

**访问路径**：
- 管理员：`/admin/change_password`
- 读者：`/reader/change_password`

**操作步骤**：
1. 在导航菜单中点击"修改密码"链接
2. 在修改密码页面输入当前密码和新密码
3. 点击"提交"按钮保存修改

**输入参数**：
- 当前密码：密码字段，必填
- 新密码：密码字段，必填
- 确认新密码：密码字段，必填

**输出结果**：
- 修改成功：显示成功提示信息
- 修改失败：显示错误提示信息

**业务规则**：
- 新密码不能与当前密码相同
- 新密码长度不少于6个字符

**可能的错误情况**：
- 当前密码错误：显示"当前密码错误"错误提示
- 新密码与确认密码不一致：显示"两次输入的新密码不一致"错误提示
- 新密码不符合规则：显示"新密码长度不能少于6个字符"错误提示

**界面描述**：
修改密码页面包含一个表单，表单包含当前密码输入框、新密码输入框、确认新密码输入框和提交按钮。

## 3. 管理员功能

### 3.1 仪表盘

**功能描述**：管理员登录后的首页，显示系统的关键统计数据和最近的借阅记录。

**访问路径**：`/admin/dashboard`

**操作步骤**：
1. 管理员登录系统
2. 系统自动跳转到仪表盘页面
3. 查看统计数据和最近借阅记录

**输出结果**：
- 统计数据：读者总数、图书总数、当前借阅数、逾期借阅数
- 最近借阅记录：显示最近5条借阅记录
- 库存预警：显示库存数量少于3本的图书
- 借阅趋势图：显示最近30天的借阅数量趋势
- 图书类别统计：显示各类别的图书数量分布

**界面描述**：
仪表盘页面采用卡片式布局，顶部显示4个统计数据卡片，中间显示借阅趋势图和图书类别统计图，底部显示最近借阅记录表格和库存预警表格。

### 3.2 读者管理

#### 3.2.1 读者列表

**功能描述**：显示所有读者的列表，支持搜索、筛选和分页。

**访问路径**：`/admin/readers`

**操作步骤**：
1. 在左侧导航菜单中点击"读者管理"
2. 系统显示读者列表页面
3. 可以使用搜索框搜索特定读者
4. 可以使用筛选器按读者类型筛选
5. 可以点击表格标题排序
6. 可以使用分页控件浏览更多读者

**输入参数**：
- 搜索关键词：文本字段，可选，用于搜索读者ID、姓名、院系或邮箱
- 读者类型：下拉选择框，可选，用于按读者类型筛选
- 排序字段：点击表格标题，可选
- 页码：分页控件，可选

**输出结果**：
- 读者列表：显示符合条件的读者信息，包括读者ID、姓名、院系、读者类型等
- 分页信息：显示当前页码和总页数

**业务规则**：
- 默认每页显示10条记录
- 默认按读者ID排序

**可能的错误情况**：
- 无匹配结果：显示"未找到匹配的读者"提示信息

**界面描述**：
读者列表页面顶部有搜索框和筛选器，中间是读者信息表格，底部是分页控件。表格包含读者ID、姓名、院系、读者类型、操作等列。操作列包含"查看"、"编辑"、"重置密码"按钮。

#### 3.2.2 添加读者

**功能描述**：添加新的读者账号。

**访问路径**：`/admin/reader/create`

**操作步骤**：
1. 在读者列表页面点击"添加读者"按钮
2. 在添加读者表单中填写读者信息
3. 点击"保存"按钮提交表单

**输入参数**：
- 读者ID：文本字段，必填，唯一标识符（通常为学号或工号）
- 姓名：文本字段，必填
- 院系：文本字段，可选
- 电话：文本字段，可选
- 邮箱：文本字段，可选
- 读者类型：下拉选择框，必填，选择读者类型（如学生、教师等）
- 密码：密码字段，必填，默认为"123456"

**输出结果**：
- 添加成功：显示成功提示信息，返回读者列表页面
- 添加失败：显示错误提示信息

**业务规则**：
- 读者ID必须唯一
- 密码默认为"123456"

**可能的错误情况**：
- 读者ID已存在：显示"读者ID已存在"错误提示
- 必填字段为空：显示"请填写必填字段"错误提示

**界面描述**：
添加读者页面包含一个表单，表单包含读者ID、姓名、院系、电话、邮箱、读者类型、密码等输入字段，以及"保存"和"取消"按钮。

#### 3.2.3 编辑读者

**功能描述**：编辑现有读者的信息。

**访问路径**：`/admin/reader/<reader_id>/edit`

**操作步骤**：
1. 在读者列表页面点击特定读者行的"编辑"按钮
2. 在编辑读者表单中修改读者信息
3. 点击"保存"按钮提交表单

**输入参数**：
- 姓名：文本字段，必填
- 院系：文本字段，可选
- 电话：文本字段，可选
- 邮箱：文本字段，可选
- 读者类型：下拉选择框，必填，选择读者类型（如学生、教师等）

**输出结果**：
- 编辑成功：显示成功提示信息，返回读者列表页面
- 编辑失败：显示错误提示信息

**业务规则**：
- 读者ID不可修改
- 密码不在此处修改，需使用"重置密码"功能

**可能的错误情况**：
- 必填字段为空：显示"请填写必填字段"错误提示

**界面描述**：
编辑读者页面包含一个表单，表单包含姓名、院系、电话、邮箱、读者类型等输入字段，以及"保存"和"取消"按钮。读者ID字段显示但不可编辑。

#### 3.2.4 读者详情

**功能描述**：查看读者的详细信息和借阅历史。

**访问路径**：`/admin/reader/<reader_id>`

**操作步骤**：
1. 在读者列表页面点击特定读者行的"查看"按钮
2. 系统显示读者详情页面

**输出结果**：
- 读者基本信息：显示读者ID、姓名、院系、电话、邮箱、读者类型等
- 借阅统计：显示当前借阅数量、历史借阅数量、逾期次数等
- 当前借阅：显示未归还的借阅记录
- 借阅历史：显示已归还的借阅记录

**界面描述**：
读者详情页面顶部显示读者基本信息卡片，下方是借阅统计卡片，再下方是当前借阅表格和借阅历史表格。页面右上角有"编辑"和"返回"按钮。

#### 3.2.5 重置密码

**功能描述**：将读者的密码重置为默认值。

**访问路径**：`/admin/reader/<reader_id>/reset_password`

**操作步骤**：
1. 在读者列表页面点击特定读者行的"重置密码"按钮
2. 系统弹出确认对话框
3. 点击"确认"按钮执行重置操作

**输出结果**：
- 重置成功：显示成功提示信息
- 重置失败：显示错误提示信息

**业务规则**：
- 默认密码为"123456"

**界面描述**：
重置密码功能没有独立页面，在读者列表页面通过按钮触发，操作通过确认对话框完成。

### 3.3 图书管理

#### 3.3.1 图书列表

**功能描述**：显示所有图书的列表，支持搜索、筛选和分页。

**访问路径**：`/admin/books`

**操作步骤**：
1. 在左侧导航菜单中点击"图书管理"
2. 系统显示图书列表页面
3. 可以使用搜索框搜索特定图书
4. 可以使用筛选器按图书类别筛选
5. 可以点击表格标题排序
6. 可以使用分页控件浏览更多图书

**输入参数**：
- 搜索关键词：文本字段，可选，用于搜索ISBN、书名、作者或出版社
- 图书类别：下拉选择框，可选，用于按图书类别筛选
- 排序字段：点击表格标题，可选
- 页码：分页控件，可选

**输出结果**：
- 图书列表：显示符合条件的图书信息，包括ISBN、书名、作者、出版社、类别、总数量、可借数量等
- 分页信息：显示当前页码和总页数

**业务规则**：
- 默认每页显示10条记录
- 默认按ISBN排序

**可能的错误情况**：
- 无匹配结果：显示"未找到匹配的图书"提示信息

**界面描述**：
图书列表页面顶部有搜索框和筛选器，中间是图书信息表格，底部是分页控件。表格包含ISBN、书名、作者、出版社、类别、总数量、可借数量、操作等列。操作列包含"查看"、"编辑"、"删除"按钮。

#### 3.3.2 添加图书

**功能描述**：添加新的图书记录。

**访问路径**：`/admin/book/create`

**操作步骤**：
1. 在图书列表页面点击"添加图书"按钮
2. 在添加图书表单中填写图书信息
3. 点击"保存"按钮提交表单

**输入参数**：
- ISBN：文本字段，必填，唯一标识符
- 书名：文本字段，必填
- 作者：文本字段，必填
- 出版社：文本字段，可选
- 出版年份：数字字段，可选
- 图书类别：下拉选择框，必填，选择图书类别
- 总数量：数字字段，必填，默认为1
- 可借数量：数字字段，必填，默认等于总数量

**输出结果**：
- 添加成功：显示成功提示信息，返回图书列表页面
- 添加失败：显示错误提示信息

**业务规则**：
- ISBN必须唯一
- 可借数量不能大于总数量

**可能的错误情况**：
- ISBN已存在：显示"ISBN已存在"错误提示
- 必填字段为空：显示"请填写必填字段"错误提示
- 可借数量大于总数量：显示"可借数量不能大于总数量"错误提示

**界面描述**：
添加图书页面包含一个表单，表单包含ISBN、书名、作者、出版社、出版年份、图书类别、总数量、可借数量等输入字段，以及"保存"和"取消"按钮。

#### 3.3.3 编辑图书

**功能描述**：编辑现有图书的信息。

**访问路径**：`/admin/book/<isbn>/edit`

**操作步骤**：
1. 在图书列表页面点击特定图书行的"编辑"按钮
2. 在编辑图书表单中修改图书信息
3. 点击"保存"按钮提交表单

**输入参数**：
- 书名：文本字段，必填
- 作者：文本字段，必填
- 出版社：文本字段，可选
- 出版年份：数字字段，可选
- 图书类别：下拉选择框，必填，选择图书类别
- 总数量：数字字段，必填
- 可借数量：数字字段，必填

**输出结果**：
- 编辑成功：显示成功提示信息，返回图书列表页面
- 编辑失败：显示错误提示信息

**业务规则**：
- ISBN不可修改
- 可借数量不能大于总数量
- 可借数量不能小于（总数量-已借出数量）

**可能的错误情况**：
- 必填字段为空：显示"请填写必填字段"错误提示
- 可借数量大于总数量：显示"可借数量不能大于总数量"错误提示
- 可借数量小于（总数量-已借出数量）：显示"可借数量不能小于已借出数量"错误提示

**界面描述**：
编辑图书页面包含一个表单，表单包含书名、作者、出版社、出版年份、图书类别、总数量、可借数量等输入字段，以及"保存"和"取消"按钮。ISBN字段显示但不可编辑。

#### 3.3.4 图书详情

**功能描述**：查看图书的详细信息和借阅历史。

**访问路径**：`/admin/book/<isbn>`

**操作步骤**：
1. 在图书列表页面点击特定图书行的"查看"按钮
2. 系统显示图书详情页面

**输出结果**：
- 图书基本信息：显示ISBN、书名、作者、出版社、出版年份、图书类别、总数量、可借数量等
- 借阅统计：显示当前借阅数量、历史借阅数量等
- 借阅历史：显示该图书的所有借阅记录

**界面描述**：
图书详情页面顶部显示图书基本信息卡片，下方是借阅统计卡片，再下方是借阅历史表格。页面右上角有"编辑"和"返回"按钮。

#### 3.3.5 删除图书

**功能描述**：删除图书记录。

**访问路径**：`/admin/book/<isbn>/delete`

**操作步骤**：
1. 在图书列表页面点击特定图书行的"删除"按钮
2. 系统弹出确认对话框
3. 点击"确认"按钮执行删除操作

**输出结果**：
- 删除成功：显示成功提示信息
- 删除失败：显示错误提示信息

**业务规则**：
- 只有当图书没有关联的借阅记录时才能删除
- 如果图书有借阅记录，需要先删除所有借阅记录才能删除图书

**可能的错误情况**：
- 图书有关联的借阅记录：显示"该图书有借阅记录，无法删除"错误提示

**界面描述**：
删除图书功能没有独立页面，在图书列表页面通过按钮触发，操作通过确认对话框完成。

### 3.4 借阅管理

#### 3.4.1 借阅记录列表

**功能描述**：显示所有借阅记录的列表，支持搜索、筛选和分页。

**访问路径**：`/admin/loans`

**操作步骤**：
1. 在左侧导航菜单中点击"借阅管理"
2. 系统显示借阅记录列表页面
3. 可以使用搜索框搜索特定借阅记录
4. 可以使用筛选器按状态筛选（如当前借阅、已归还、逾期等）
5. 可以点击表格标题排序
6. 可以使用分页控件浏览更多借阅记录

**输入参数**：
- 搜索关键词：文本字段，可选，用于搜索读者ID、读者姓名、ISBN或书名
- 状态筛选：下拉选择框，可选，用于按借阅状态筛选
- 排序字段：点击表格标题，可选
- 页码：分页控件，可选

**输出结果**：
- 借阅记录列表：显示符合条件的借阅记录，包括借阅ID、读者信息、图书信息、借阅日期、应还日期、归还日期、状态等
- 分页信息：显示当前页码和总页数

**业务规则**：
- 默认每页显示10条记录
- 默认按借阅日期降序排序

**可能的错误情况**：
- 无匹配结果：显示"未找到匹配的借阅记录"提示信息

**界面描述**：
借阅记录列表页面顶部有搜索框和筛选器，中间是借阅记录表格，底部是分页控件。表格包含借阅ID、读者ID、读者姓名、ISBN、书名、借阅日期、应还日期、归还日期、状态、操作等列。操作列包含"查看"、"归还"、"续借"按钮（根据借阅状态显示不同按钮）。

#### 3.4.2 创建借阅

**功能描述**：为读者借阅图书。

**访问路径**：`/admin/loan/create`

**操作步骤**：
1. 在借阅记录列表页面点击"创建借阅"按钮
2. 在创建借阅表单中选择读者和图书
3. 点击"保存"按钮提交表单

**输入参数**：
- 读者：下拉选择框或搜索框，必填，选择借阅的读者
- 图书：下拉选择框或搜索框，必填，选择借阅的图书

**输出结果**：
- 创建成功：显示成功提示信息，返回借阅记录列表页面
- 创建失败：显示错误提示信息

**业务规则**：
- 读者必须存在且未被禁用
- 图书必须存在且有可借数量
- 读者当前借阅数量不能超过其读者类型允许的最大借阅数量
- 读者不能重复借阅同一本图书
- 借阅期限根据读者类型自动计算

**可能的错误情况**：
- 读者不存在：显示"读者不存在"错误提示
- 图书不存在：显示"图书不存在"错误提示
- 图书无可借数量：显示"图书已无可借数量"错误提示
- 读者已达最大借阅数量：显示"读者已达最大借阅数量"错误提示
- 读者已借阅此图书：显示"读者已借阅此图书"错误提示

**界面描述**：
创建借阅页面包含一个表单，表单包含读者选择框和图书选择框，以及"保存"和"取消"按钮。读者选择框支持按ID或姓名搜索，图书选择框支持按ISBN或书名搜索。

**借阅流程图**：

```mermaid
flowchart TD
    A[进入创建借阅页面] --> B[选择读者]
    B --> C[选择图书]
    C --> D[点击保存按钮]
    D --> E{验证读者和图书}
    E -->|验证失败| F[显示错误提示]
    F --> B
    E -->|验证成功| G{检查业务规则}
    G -->|不符合规则| H[显示错误提示]
    H --> B
    G -->|符合规则| I[创建借阅记录]
    I --> J[更新图书可借数量]
    J --> K[显示成功提示]
    K --> L[返回借阅记录列表]
```

#### 3.4.3 借阅详情

**功能描述**：查看借阅记录的详细信息。

**访问路径**：`/admin/loan/<loan_id>`

**操作步骤**：
1. 在借阅记录列表页面点击特定借阅记录行的"查看"按钮
2. 系统显示借阅详情页面

**输出结果**：
- 借阅基本信息：显示借阅ID、读者信息、图书信息、借阅日期、应还日期、归还日期、状态等
- 续借信息：显示续借次数和最大续借次数
- 罚款信息：如果逾期，显示逾期天数和罚款金额

**界面描述**：
借阅详情页面显示借阅记录的所有信息，包括借阅ID、读者信息、图书信息、借阅日期、应还日期、归还日期、状态、续借次数、逾期天数、罚款金额等。页面右上角有"归还"、"续借"和"返回"按钮（根据借阅状态显示不同按钮）。

#### 3.4.4 归还图书

**功能描述**：处理图书归还，计算逾期罚款。

**访问路径**：`/admin/loan/<loan_id>/return`

**操作步骤**：
1. 在借阅记录列表页面或借阅详情页面点击"归还"按钮
2. 系统显示归还确认页面，如果逾期，显示逾期天数和罚款金额
3. 点击"确认归还"按钮执行归还操作

**输出结果**：
- 归还成功：显示成功提示信息，返回借阅记录列表页面
- 归还失败：显示错误提示信息

**业务规则**：
- 只有未归还的借阅记录才能执行归还操作
- 如果逾期，系统自动计算逾期天数和罚款金额
- 罚款金额 = 逾期天数 × 图书类别的罚款金额
- 归还后，相应图书的可借数量增加1

**可能的错误情况**：
- 借阅记录不存在：显示"借阅记录不存在"错误提示
- 借阅记录已归还：显示"此借阅已归还"错误提示

**界面描述**：
归还确认页面显示借阅记录的基本信息，如果逾期，显示逾期天数和罚款金额。页面底部有"确认归还"和"取消"按钮。

**归还流程图**：

```mermaid
flowchart TD
    A[点击归还按钮] --> B[显示归还确认页面]
    B --> C{检查是否逾期}
    C -->|是| D[计算逾期天数和罚款金额]
    C -->|否| E[无需罚款]
    D --> F[显示罚款信息]
    E --> G[点击确认归还按钮]
    F --> G
    G --> H[更新借阅记录]
    H --> I[更新图书可借数量]
    I --> J[显示成功提示]
    J --> K[返回借阅记录列表]
```

#### 3.4.5 续借图书

**功能描述**：为读者续借图书，延长借阅期限。

**访问路径**：`/admin/loan/<loan_id>/renew`

**操作步骤**：
1. 在借阅记录列表页面或借阅详情页面点击"续借"按钮
2. 系统显示续借确认页面，显示当前应还日期和续借后的应还日期
3. 点击"确认续借"按钮执行续借操作

**输出结果**：
- 续借成功：显示成功提示信息，返回借阅记录列表页面
- 续借失败：显示错误提示信息

**业务规则**：
- 只有未归还且未逾期的借阅记录才能执行续借操作
- 续借次数不能超过读者类型允许的最大续借次数
- 续借后的应还日期 = 当前应还日期 + 读者类型的借阅期限

**可能的错误情况**：
- 借阅记录不存在：显示"借阅记录不存在"错误提示
- 借阅记录已归还：显示"此借阅已归还，不能续借"错误提示
- 借阅记录已逾期：显示"此借阅已逾期，不能续借"错误提示
- 已达最大续借次数：显示"已达到最大续借次数"错误提示

**界面描述**：
续借确认页面显示借阅记录的基本信息，包括当前应还日期、续借后的应还日期、已续借次数和最大续借次数。页面底部有"确认续借"和"取消"按钮。

**续借流程图**：

```mermaid
flowchart TD
    A[点击续借按钮] --> B[显示续借确认页面]
    B --> C{检查是否已归还}
    C -->|是| D[显示错误提示]
    C -->|否| E{检查是否逾期}
    E -->|是| F[显示错误提示]
    E -->|否| G{检查续借次数}
    G -->|已达上限| H[显示错误提示]
    G -->|未达上限| I[计算新的应还日期]
    I --> J[点击确认续借按钮]
    J --> K[更新借阅记录]
    K --> L[显示成功提示]
    L --> M[返回借阅记录列表]
    D --> N[返回借阅记录列表]
    F --> N
    H --> N
```

### 3.5 统计分析

#### 3.5.1 借阅统计

**功能描述**：显示借阅相关的统计数据和图表。

**访问路径**：`/admin/statistics/loans`

**操作步骤**：
1. 在左侧导航菜单中点击"统计分析"下的"借阅统计"
2. 系统显示借阅统计页面

**输入参数**：
- 时间范围：下拉选择框，可选，选择统计的时间范围（如今天、本周、本月、本年等）

**输出结果**：
- 借阅总数：显示指定时间范围内的借阅总数
- 归还总数：显示指定时间范围内的归还总数
- 逾期总数：显示指定时间范围内的逾期总数
- 罚款总额：显示指定时间范围内的罚款总额
- 借阅趋势图：显示指定时间范围内的每日借阅数量趋势
- 热门图书排行：显示指定时间范围内借阅次数最多的图书
- 活跃读者排行：显示指定时间范围内借阅次数最多的读者

**界面描述**：
借阅统计页面顶部有时间范围选择框，下方是统计数据卡片，再下方是借阅趋势图、热门图书排行表格和活跃读者排行表格。

#### 3.5.2 图书统计

**功能描述**：显示图书相关的统计数据和图表。

**访问路径**：`/admin/statistics/books`

**操作步骤**：
1. 在左侧导航菜单中点击"统计分析"下的"图书统计"
2. 系统显示图书统计页面

**输出结果**：
- 图书总数：显示系统中的图书总数
- 可借图书数：显示系统中可借的图书数量
- 借出图书数：显示系统中已借出的图书数量
- 图书类别分布：显示各类别的图书数量分布
- 出版年份分布：显示各出版年份的图书数量分布
- 库存预警：显示库存数量少于3本的图书

**界面描述**：
图书统计页面顶部是统计数据卡片，下方是图书类别分布图、出版年份分布图和库存预警表格。

## 4. 读者功能

### 4.1 个人仪表盘

**功能描述**：读者登录后的首页，显示个人借阅统计和当前借阅记录。

**访问路径**：`/reader/dashboard`

**操作步骤**：
1. 读者登录系统
2. 系统自动跳转到个人仪表盘页面
3. 查看借阅统计和当前借阅记录

**输出结果**：
- 借阅统计：显示当前借阅数量、历史借阅数量、可借阅数量等
- 当前借阅：显示未归还的借阅记录
- 即将到期：显示即将到期（3天内）的借阅记录
- 已逾期：显示已逾期的借阅记录

**界面描述**：
个人仪表盘页面顶部显示借阅统计卡片，下方显示当前借阅表格、即将到期表格和已逾期表格。

### 4.2 图书检索

#### 4.2.1 图书列表

**功能描述**：显示所有可借图书的列表，支持搜索、筛选和分页。

**访问路径**：`/reader/books`

**操作步骤**：
1. 在左侧导航菜单中点击"图书检索"
2. 系统显示图书列表页面
3. 可以使用搜索框搜索特定图书
4. 可以使用筛选器按图书类别筛选
5. 可以点击表格标题排序
6. 可以使用分页控件浏览更多图书

**输入参数**：
- 搜索关键词：文本字段，可选，用于搜索ISBN、书名、作者或出版社
- 图书类别：下拉选择框，可选，用于按图书类别筛选
- 排序字段：点击表格标题，可选
- 页码：分页控件，可选

**输出结果**：
- 图书列表：显示符合条件的图书信息，包括ISBN、书名、作者、出版社、类别、可借数量等
- 分页信息：显示当前页码和总页数

**业务规则**：
- 默认每页显示10条记录
- 默认按书名排序
- 只显示可借数量大于0的图书

**可能的错误情况**：
- 无匹配结果：显示"未找到匹配的图书"提示信息

**界面描述**：
图书列表页面顶部有搜索框和筛选器，中间是图书信息表格，底部是分页控件。表格包含ISBN、书名、作者、出版社、类别、可借数量、操作等列。操作列包含"查看"和"借阅"按钮。

#### 4.2.2 图书详情

**功能描述**：查看图书的详细信息。

**访问路径**：`/reader/book/<isbn>`

**操作步骤**：
1. 在图书列表页面点击特定图书行的"查看"按钮
2. 系统显示图书详情页面

**输出结果**：
- 图书基本信息：显示ISBN、书名、作者、出版社、出版年份、图书类别、可借数量等

**界面描述**：
图书详情页面显示图书的所有信息，包括ISBN、书名、作者、出版社、出版年份、图书类别、可借数量等。页面右上角有"借阅"和"返回"按钮。

#### 4.2.3 借阅图书

**功能描述**：读者借阅图书。

**访问路径**：`/reader/book/<isbn>/borrow`

**操作步骤**：
1. 在图书列表页面或图书详情页面点击"借阅"按钮
2. 系统弹出确认对话框
3. 点击"确认"按钮执行借阅操作

**输出结果**：
- 借阅成功：显示成功提示信息
- 借阅失败：显示错误提示信息

**业务规则**：
- 图书必须有可借数量
- 读者当前借阅数量不能超过其读者类型允许的最大借阅数量
- 读者不能重复借阅同一本图书
- 借阅期限根据读者类型自动计算

**可能的错误情况**：
- 图书无可借数量：显示"图书已无可借数量"错误提示
- 读者已达最大借阅数量：显示"您已达到最大借阅数量"错误提示
- 读者已借阅此图书：显示"您已借阅此图书"错误提示

**界面描述**：
借阅图书功能没有独立页面，在图书列表页面或图书详情页面通过按钮触发，操作通过确认对话框完成。

**借阅流程图**：

```mermaid
flowchart TD
    A[点击借阅按钮] --> B[显示确认对话框]
    B --> C[点击确认按钮]
    C --> D{检查图书可借数量}
    D -->|无可借数量| E[显示错误提示]
    D -->|有可借数量| F{检查读者借阅数量}
    F -->|已达上限| G[显示错误提示]
    F -->|未达上限| H{检查是否已借阅此图书}
    H -->|是| I[显示错误提示]
    H -->|否| J[创建借阅记录]
    J --> K[更新图书可借数量]
    K --> L[显示成功提示]
    E --> M[返回图书列表]
    G --> M
    I --> M
    L --> M
```

### 4.3 借阅记录

#### 4.3.1 当前借阅

**功能描述**：显示读者当前未归还的借阅记录。

**访问路径**：`/reader/loans`

**操作步骤**：
1. 在左侧导航菜单中点击"借阅记录"
2. 系统显示当前借阅页面

**输出结果**：
- 当前借阅列表：显示未归还的借阅记录，包括图书信息、借阅日期、应还日期、状态等

**界面描述**：
当前借阅页面显示一个表格，包含图书信息、借阅日期、应还日期、状态、操作等列。操作列包含"归还"和"续借"按钮（根据借阅状态显示不同按钮）。

#### 4.3.2 借阅历史

**功能描述**：显示读者已归还的借阅记录。

**访问路径**：`/reader/loans/history`

**操作步骤**：
1. 在左侧导航菜单中点击"借阅记录"
2. 点击页面上的"查看历史借阅"按钮
3. 系统显示借阅历史页面

**输出结果**：
- 借阅历史列表：显示已归还的借阅记录，包括图书信息、借阅日期、应还日期、归还日期、状态等

**界面描述**：
借阅历史页面显示一个表格，包含图书信息、借阅日期、应还日期、归还日期、状态等列。

#### 4.3.3 归还图书

**功能描述**：读者归还图书。

**访问路径**：`/reader/loan/<loan_id>/return`

**操作步骤**：
1. 在当前借阅页面点击特定借阅记录行的"归还"按钮
2. 系统弹出确认对话框，如果逾期，显示逾期天数和罚款金额
3. 点击"确认归还"按钮执行归还操作

**输出结果**：
- 归还成功：显示成功提示信息
- 归还失败：显示错误提示信息

**业务规则**：
- 只有未归还的借阅记录才能执行归还操作
- 如果逾期，系统自动计算逾期天数和罚款金额
- 罚款金额 = 逾期天数 × 图书类别的罚款金额
- 归还后，相应图书的可借数量增加1

**可能的错误情况**：
- 借阅记录不存在：显示"借阅记录不存在"错误提示
- 借阅记录已归还：显示"此借阅已归还"错误提示

**界面描述**：
归还图书功能没有独立页面，在当前借阅页面通过按钮触发，操作通过确认对话框完成。

**归还流程图**：

```mermaid
flowchart TD
    A[点击归还按钮] --> B[显示确认对话框]
    B --> C{检查是否逾期}
    C -->|是| D[计算逾期天数和罚款金额]
    C -->|否| E[无需罚款]
    D --> F[显示罚款信息]
    E --> G[点击确认归还按钮]
    F --> G
    G --> H[更新借阅记录]
    H --> I[更新图书可借数量]
    I --> J[显示成功提示]
    J --> K[返回当前借阅页面]
```

#### 4.3.4 续借图书

**功能描述**：读者续借图书，延长借阅期限。

**访问路径**：`/reader/loan/<loan_id>/renew`

**操作步骤**：
1. 在当前借阅页面点击特定借阅记录行的"续借"按钮
2. 系统弹出确认对话框，显示当前应还日期和续借后的应还日期
3. 点击"确认续借"按钮执行续借操作

**输出结果**：
- 续借成功：显示成功提示信息
- 续借失败：显示错误提示信息

**业务规则**：
- 只有未归还且未逾期的借阅记录才能执行续借操作
- 续借次数不能超过读者类型允许的最大续借次数
- 续借后的应还日期 = 当前应还日期 + 读者类型的借阅期限

**可能的错误情况**：
- 借阅记录不存在：显示"借阅记录不存在"错误提示
- 借阅记录已归还：显示"此借阅已归还，不能续借"错误提示
- 借阅记录已逾期：显示"此借阅已逾期，不能续借"错误提示
- 已达最大续借次数：显示"已达到最大续借次数"错误提示

**界面描述**：
续借图书功能没有独立页面，在当前借阅页面通过按钮触发，操作通过确认对话框完成。

**续借流程图**：

```mermaid
flowchart TD
    A[点击续借按钮] --> B[显示确认对话框]
    B --> C{检查是否已归还}
    C -->|是| D[显示错误提示]
    C -->|否| E{检查是否逾期}
    E -->|是| F[显示错误提示]
    E -->|否| G{检查续借次数}
    G -->|已达上限| H[显示错误提示]
    G -->|未达上限| I[计算新的应还日期]
    I --> J[点击确认续借按钮]
    J --> K[更新借阅记录]
    K --> L[显示成功提示]
    L --> M[返回当前借阅页面]
    D --> N[返回当前借阅页面]
    F --> N
    H --> N
```

### 4.4 个人信息

#### 4.4.1 查看个人信息

**功能描述**：查看个人基本信息。

**访问路径**：`/reader/profile`

**操作步骤**：
1. 在左侧导航菜单中点击"个人信息"
2. 系统显示个人信息页面

**输出结果**：
- 个人基本信息：显示读者ID、姓名、院系、电话、邮箱、读者类型等
- 借阅权限信息：显示最大借阅数量、借阅期限、最大续借次数等

**界面描述**：
个人信息页面显示读者的所有信息，包括读者ID、姓名、院系、电话、邮箱、读者类型、最大借阅数量、借阅期限、最大续借次数等。页面右上角有"修改密码"按钮。

#### 4.4.2 修改密码

**功能描述**：修改个人登录密码。

**访问路径**：`/reader/change_password`

**操作步骤**：
1. 在个人信息页面点击"修改密码"按钮
2. 在修改密码页面输入当前密码和新密码
3. 点击"提交"按钮保存修改

**输入参数**：
- 当前密码：密码字段，必填
- 新密码：密码字段，必填
- 确认新密码：密码字段，必填

**输出结果**：
- 修改成功：显示成功提示信息
- 修改失败：显示错误提示信息

**业务规则**：
- 新密码不能与当前密码相同
- 新密码长度不少于6个字符

**可能的错误情况**：
- 当前密码错误：显示"当前密码错误"错误提示
- 新密码与确认密码不一致：显示"两次输入的新密码不一致"错误提示
- 新密码不符合规则：显示"新密码长度不能少于6个字符"错误提示

**界面描述**：
修改密码页面包含一个表单，表单包含当前密码输入框、新密码输入框、确认新密码输入框和提交按钮。
