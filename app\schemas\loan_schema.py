#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
借阅相关数据验证和序列化模式
"""

from marshmallow import fields, validate, validates, ValidationError

from .. import ma
from ..models.loan import Loan
from ..models.reader import Reader
from ..models.book import Book


class LoanSchema(ma.SQLAlchemyAutoSchema):
    """借阅模式"""
    class Meta:
        model = Loan
        load_instance = True
        include_fk = True
    
    loan_id = fields.Integer(dump_only=True)
    reader_id = fields.String(required=True, validate=validate.Length(min=1, max=10))
    isbn = fields.String(required=True, validate=validate.Length(min=1, max=20))
    loan_date = fields.DateTime(required=True)
    due_date = fields.DateTime(required=True)
    return_date = fields.DateTime()
    fine_amount = fields.Decimal(places=2)
    
    @validates('reader_id')
    def validate_reader_id(self, value):
        """验证读者是否存在"""
        reader = Reader.query.get(value)
        if not reader:
            raise ValidationError(f'读者 {value} 不存在')
    
    @validates('isbn')
    def validate_isbn(self, value):
        """验证图书是否存在"""
        book = Book.query.get(value)
        if not book:
            raise ValidationError(f'图书 {value} 不存在')
        if not book.is_available():
            raise ValidationError(f'图书 {value} 已无可借数量')


class LoanCreateSchema(ma.Schema):
    """借阅创建模式"""
    reader_id = fields.String(required=True, validate=validate.Length(min=1, max=10))
    isbn = fields.String(required=True, validate=validate.Length(min=1, max=20))
    
    @validates('reader_id')
    def validate_reader_id(self, value):
        """验证读者是否存在且可以借书"""
        reader = Reader.query.get(value)
        if not reader:
            raise ValidationError(f'读者 {value} 不存在')
        if not reader.can_borrow():
            raise ValidationError(f'读者 {value} 已达到最大借阅数量')
    
    @validates('isbn')
    def validate_isbn(self, value):
        """验证图书是否存在且可借"""
        book = Book.query.get(value)
        if not book:
            raise ValidationError(f'图书 {value} 不存在')
        if not book.is_available():
            raise ValidationError(f'图书 {value} 已无可借数量')


class LoanReturnSchema(ma.Schema):
    """借阅归还模式"""
    loan_id = fields.Integer(required=True)
    return_date = fields.DateTime(required=True)
    
    @validates('loan_id')
    def validate_loan_id(self, value):
        """验证借阅记录是否存在且未归还"""
        loan = Loan.query.get(value)
        if not loan:
            raise ValidationError(f'借阅记录 {value} 不存在')
        if loan.return_date is not None:
            raise ValidationError(f'借阅记录 {value} 已归还')


class LoanRenewSchema(ma.Schema):
    """借阅续借模式"""
    loan_id = fields.Integer(required=True)
    
    @validates('loan_id')
    def validate_loan_id(self, value):
        """验证借阅记录是否存在且可续借"""
        loan = Loan.query.get(value)
        if not loan:
            raise ValidationError(f'借阅记录 {value} 不存在')
        if loan.return_date is not None:
            raise ValidationError(f'借阅记录 {value} 已归还')
        if loan.is_overdue():
            raise ValidationError(f'借阅记录 {value} 已逾期，不能续借')
