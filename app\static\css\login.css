/*
 * 图书借阅管理系统登录页面样式
 */

/* 登录页面背景 */
.login-page {
    min-height: 100vh;
    max-height: 100vh;
    display: flex;
    align-items: stretch;
    padding: 0;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
}

/* 左侧背景区域 (65%) */
.login-bg {
    flex: 65%;
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    padding: 2rem;
}

.login-bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 58, 95, 0.95), rgba(66, 153, 225, 0.9));
    z-index: 1;
}

.login-bg-image {
    width: auto;
    height: auto;
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
    position: relative;
    z-index: 2;
}

/* 右侧登录区域 (35%) */
.login-form-container {
    flex: 35%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background-color: #f8fafc;
}

/* 登录卡片 */
.login-card {
    background-color: white;
    border-radius: 1.25rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    overflow: hidden;
    margin: 0 auto;
    animation-duration: 0.8s;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 登录卡片头部 */
.login-header {
    padding: 2.5rem 2rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background: linear-gradient(to bottom, #ffffff, #f8fafc);
}

.login-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 4px 8px rgba(66, 153, 225, 0.2));
    transition: transform 0.3s ease, filter 0.3s ease;
}

.login-logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 6px 12px rgba(66, 153, 225, 0.3));
}

.login-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.5px;
}

.login-subtitle {
    color: var(--text-muted);
    font-size: 1rem;
    margin-bottom: 0;
    line-height: 1.5;
    padding: 0 1rem;
}

/* 登录卡片主体 */
.login-body {
    padding: 2.5rem 2rem;
}

/* 登录表单 */
.login-body .form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}

.login-body .input-group {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.login-body .input-group:focus-within {
    box-shadow: 0 5px 15px rgba(66, 153, 225, 0.15);
    transform: translateY(-1px);
}

.login-body .input-group-text {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    color: var(--primary-dark);
    border-top-left-radius: 0.75rem;
    border-bottom-left-radius: 0.75rem;
    padding: 0.85rem 1rem;
    transition: all 0.3s ease;
}

.login-body .form-control {
    border-color: #e2e8f0;
    padding: 0.85rem 1rem;
    font-size: 1rem;
    border-top-right-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
    transition: all 0.3s ease;
}

.login-body .form-control:focus {
    border-color: var(--primary-light);
    box-shadow: none;
    background-color: #fff;
}

.login-body .btn-group {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
    border-radius: 0.75rem;
    margin-bottom: 2rem;
    overflow: hidden;
    display: flex;
    flex-wrap: nowrap;
}

.login-body .btn-check:checked + .btn-outline-primary {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
    border-color: var(--primary-dark);
    color: white;
    box-shadow: 0 4px 10px rgba(66, 153, 225, 0.25);
}

.login-body .btn-outline-primary {
    color: var(--primary-dark);
    border-color: var(--primary-dark);
    padding: 0.85rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
    flex: 1;
    white-space: nowrap;
    min-width: 0;
    text-overflow: ellipsis;
}

.login-body .btn-outline-primary:hover {
    background-color: rgba(26, 58, 95, 0.05);
    color: var(--primary-dark);
    transform: translateY(-1px);
}

.login-body .form-check-input:checked {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.login-body .form-check-label {
    color: var(--text-muted);
    font-size: 0.95rem;
    cursor: pointer;
}

.login-body a {
    color: var(--primary-dark);
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.login-body a:hover {
    color: var(--primary-light);
    text-decoration: underline;
    transform: translateY(-1px);
}

.login-body .btn-primary {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
    border: none;
    font-weight: 600;
    padding: 0.95rem 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 10px rgba(66, 153, 225, 0.25);
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
}

.login-body .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.35);
}

.login-body .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(66, 153, 225, 0.2);
}

/* 登录卡片底部 */
.login-footer {
    padding: 1.5rem 2rem;
    text-align: center;
    background-color: #f8fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .login-form-container {
        padding: 1.5rem;
    }

    .login-card {
        max-width: 380px;
    }
}

@media (max-width: 992px) {
    .login-page {
        flex-direction: column;
    }

    .login-bg {
        flex: none;
        height: 250px;
    }

    .login-form-container {
        flex: none;
        padding: 2rem 1.5rem;
    }

    .login-card {
        max-width: 500px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .login-bg-image {
        max-width: 250px;
        max-height: 250px;
    }
}

@media (max-width: 768px) {
    .login-bg {
        height: 200px;
    }

    .login-card {
        max-width: 450px;
    }
}

@media (max-width: 576px) {
    .login-bg {
        height: 180px;
    }

    .login-bg-image {
        max-width: 200px;
        max-height: 200px;
    }

    .login-card {
        max-width: 100%;
        margin: 0;
        border-radius: 1rem;
    }

    .login-header {
        padding: 2rem 1.5rem 1rem;
    }

    .login-logo {
        width: 60px;
        height: 60px;
        margin-bottom: 1rem;
    }

    .login-title {
        font-size: 1.5rem;
    }

    .login-subtitle {
        font-size: 0.9rem;
    }

    .login-body {
        padding: 1.5rem;
    }

    .login-body .btn-group {
        margin-bottom: 1.5rem;
    }

    .login-body .btn-outline-primary {
        padding: 0.75rem 0.5rem;
        font-size: 0.85rem;
        min-width: 0;
    }

    .login-body .btn-group {
        flex-wrap: nowrap;
    }

    .login-body .btn-primary {
        padding: 0.85rem 1.25rem;
    }

    .login-footer {
        padding: 1rem 1.5rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 400px) {
    .login-bg {
        height: 150px;
    }

    .login-form-container {
        padding: 1.5rem 1rem;
    }

    .login-body {
        padding: 1.25rem;
    }

    .login-header {
        padding: 1.5rem 1rem 1rem;
    }

    .login-logo {
        width: 50px;
        height: 50px;
    }

    .login-title {
        font-size: 1.3rem;
    }

    .login-body .btn-outline-primary {
        padding: 0.6rem 0.3rem;
        font-size: 0.8rem;
        line-height: 1.2;
    }

    .login-body .btn-group {
        margin-bottom: 1.25rem;
    }
}
