{% extends "base.html" %}

{% block title %}办理还书 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">办理还书</h1>
            <p class="text-muted mb-0">为读者办理图书归还手续</p>
        </div>
        <a href="{{ url_for('staff.dashboard') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>返回仪表盘
        </a>
    </div>

    <div class="row">
        <!-- 还书表单 -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="bi bi-arrow-return-left me-2"></i>还书信息
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('staff.process_return') }}">
                        <!-- 查找方式选择 -->
                        <div class="mb-3">
                            <label class="form-label">查找方式</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="search_type" id="by_loan_id" value="loan_id" checked>
                                <label class="btn btn-outline-primary" for="by_loan_id">
                                    <i class="bi bi-hash me-2"></i>借阅编号
                                </label>
                                
                                <input type="radio" class="btn-check" name="search_type" id="by_reader_book" value="reader_book">
                                <label class="btn btn-outline-primary" for="by_reader_book">
                                    <i class="bi bi-person-plus me-2"></i>读者+图书
                                </label>
                            </div>
                        </div>

                        <!-- 借阅编号查找 -->
                        <div id="loan_id_section">
                            <div class="mb-3">
                                <label for="loan_id" class="form-label">
                                    <i class="bi bi-hash me-1"></i>借阅编号
                                </label>
                                <input type="text" class="form-control" id="loan_id" name="loan_id" 
                                       placeholder="请输入借阅编号" autocomplete="off">
                            </div>
                        </div>

                        <!-- 读者+图书查找 -->
                        <div id="reader_book_section" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="reader_id" class="form-label">
                                        <i class="bi bi-person-vcard me-1"></i>读者编号
                                    </label>
                                    <input type="text" class="form-control" id="reader_id" name="reader_id" 
                                           placeholder="请输入读者编号" autocomplete="off">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="isbn" class="form-label">
                                        <i class="bi bi-book me-1"></i>图书ISBN
                                    </label>
                                    <input type="text" class="form-control" id="isbn" name="isbn" 
                                           placeholder="请输入图书ISBN" autocomplete="off">
                                </div>
                            </div>
                        </div>

                        <!-- 借阅信息显示 -->
                        <div id="loan_info" class="alert alert-info" style="display: none;">
                            <h6><i class="bi bi-info-circle me-2"></i>借阅信息</h6>
                            <div id="loan_details"></div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-info me-md-2" id="search_loan_btn">
                                <i class="bi bi-search me-2"></i>查找借阅记录
                            </button>
                            <button type="reset" class="btn btn-secondary me-md-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>重置
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>确认归还
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 归还规则和快速操作 -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="bi bi-info-circle me-2"></i>归还规则
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="bi bi-calendar-check text-success me-2"></i>
                            按时归还：无罚款
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-calendar-x text-warning me-2"></i>
                            逾期归还：自动计算罚款
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-currency-yen text-danger me-2"></i>
                            罚款标准：每天0.5元
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-cash-coin text-info me-2"></i>
                            可现场缴纳或后续处理
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-lightning-charge me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('staff.borrow_book') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>办理借书
                        </a>
                        <a href="{{ url_for('staff.fine_management') }}" class="btn btn-warning">
                            <i class="bi bi-currency-yen me-2"></i>罚款管理
                        </a>
                        <a href="{{ url_for('staff.loan_history') }}" class="btn btn-info">
                            <i class="bi bi-clock-history me-2"></i>借阅历史
                        </a>
                    </div>
                </div>
            </div>

            <!-- 今日归还统计 -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="bi bi-graph-up me-2"></i>今日统计
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="h4 text-success" id="today_returns">-</div>
                    <small class="text-muted">今日归还数量</small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchTypeInputs = document.querySelectorAll('input[name="search_type"]');
    const loanIdSection = document.getElementById('loan_id_section');
    const readerBookSection = document.getElementById('reader_book_section');
    const searchLoanBtn = document.getElementById('search_loan_btn');
    const loanInfo = document.getElementById('loan_info');
    const loanDetails = document.getElementById('loan_details');

    // 切换查找方式
    searchTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.value === 'loan_id') {
                loanIdSection.style.display = 'block';
                readerBookSection.style.display = 'none';
            } else {
                loanIdSection.style.display = 'none';
                readerBookSection.style.display = 'block';
            }
            loanInfo.style.display = 'none';
        });
    });

    // 查找借阅记录
    searchLoanBtn.addEventListener('click', function() {
        const searchType = document.querySelector('input[name="search_type"]:checked').value;
        let queryParams = '';

        if (searchType === 'loan_id') {
            const loanId = document.getElementById('loan_id').value.trim();
            if (!loanId) {
                alert('请输入借阅编号');
                return;
            }
            queryParams = `loan_id=${encodeURIComponent(loanId)}`;
        } else {
            const readerId = document.getElementById('reader_id').value.trim();
            const isbn = document.getElementById('isbn').value.trim();
            if (!readerId || !isbn) {
                alert('请输入读者编号和图书ISBN');
                return;
            }
            queryParams = `reader_id=${encodeURIComponent(readerId)}&isbn=${encodeURIComponent(isbn)}`;
        }

        // 这里可以添加AJAX查询借阅记录的逻辑
        // 暂时显示提示信息
        loanDetails.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">查找中...</span>
                </div>
                <p class="mt-2">正在查找借阅记录...</p>
            </div>
        `;
        loanInfo.style.display = 'block';

        // 模拟查询延迟
        setTimeout(() => {
            loanDetails.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    请直接提交表单进行归还操作，系统将自动查找并处理借阅记录。
                </div>
            `;
        }, 1000);
    });

    // 重置表单时隐藏借阅信息
    document.querySelector('button[type="reset"]').addEventListener('click', function() {
        loanInfo.style.display = 'none';
    });
});
</script>
{% endblock %}
