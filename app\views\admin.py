#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员视图
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required
from sqlalchemy import func, desc, and_
from datetime import datetime, timedelta
import pytz

from .. import db
from ..models.reader import Reader, ReaderType
from ..models.book import Book, BookCategory
from ..models.loan import Loan
from ..models.staff import Staff
from ..utils.auth import admin_required
from ..utils.time_util import get_current_time, ensure_timezone

# 创建蓝图
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')


# =====================================================
# 工作人员管理路由
# =====================================================

@admin_bp.route('/staff')
@login_required
@admin_required
def staff_list():
    """工作人员列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取搜索参数
    search = request.args.get('search', '')
    department = request.args.get('department', '')
    status = request.args.get('status', '')

    # 构建查询
    query = Staff.query

    if search:
        query = query.filter(
            db.or_(
                Staff.staff_id.like(f'%{search}%'),
                Staff.name.like(f'%{search}%'),
                Staff.phone.like(f'%{search}%')
            )
        )

    if department:
        query = query.filter(Staff.department == department)

    if status:
        query = query.filter(Staff.status == status)

    # 分页查询
    staff_list = query.order_by(Staff.hire_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取部门列表
    departments = db.session.query(Staff.department).distinct().all()
    departments = [dept[0] for dept in departments if dept[0]]

    return render_template('admin/staff_list.html',
                         staff_list=staff_list,
                         departments=departments,
                         search=search,
                         selected_department=department,
                         selected_status=status)


@admin_bp.route('/staff/add')
@login_required
@admin_required
def staff_add():
    """添加工作人员页面"""
    return render_template('admin/staff_add.html')


@admin_bp.route('/staff/add', methods=['POST'])
@login_required
@admin_required
def staff_create():
    """创建工作人员"""
    staff_id = request.form.get('staff_id')
    name = request.form.get('name')
    phone = request.form.get('phone')
    department = request.form.get('department')
    position = request.form.get('position')
    password = request.form.get('password', '123456')

    # 验证必填字段
    if not staff_id or not name:
        flash('工作证号和姓名为必填项', 'warning')
        return redirect(url_for('admin.staff_add'))

    # 检查工作证号是否已存在
    if Staff.query.get(staff_id):
        flash('工作证号已存在', 'danger')
        return redirect(url_for('admin.staff_add'))

    try:
        # 创建工作人员
        staff = Staff.create_staff(
            staff_id=staff_id,
            name=name,
            phone=phone,
            department=department,
            position=position,
            password=password
        )
        flash(f'工作人员 {name}({staff_id}) 创建成功', 'success')
        return redirect(url_for('admin.staff_list'))
    except Exception as e:
        flash(f'创建失败：{str(e)}', 'danger')
        return redirect(url_for('admin.staff_add'))


@admin_bp.route('/staff/<staff_id>')
@login_required
@admin_required
def staff_detail(staff_id):
    """工作人员详情"""
    staff = Staff.query.get_or_404(staff_id)

    # 获取工作统计
    monthly_stats = staff.get_monthly_stats()
    recent_loans = staff.get_recent_processed_loans(10)

    return render_template('admin/staff_detail.html',
                         staff=staff,
                         monthly_stats=monthly_stats,
                         recent_loans=recent_loans)


@admin_bp.route('/staff/<staff_id>/edit')
@login_required
@admin_required
def staff_edit(staff_id):
    """编辑工作人员页面"""
    staff = Staff.query.get_or_404(staff_id)
    return render_template('admin/staff_edit.html', staff=staff)


@admin_bp.route('/staff/<staff_id>/edit', methods=['POST'])
@login_required
@admin_required
def staff_update(staff_id):
    """更新工作人员信息"""
    staff = Staff.query.get_or_404(staff_id)

    name = request.form.get('name')
    phone = request.form.get('phone')
    department = request.form.get('department')
    position = request.form.get('position')
    status = request.form.get('status')

    try:
        staff.update_info(
            name=name,
            phone=phone,
            department=department,
            position=position,
            status=status
        )
        flash(f'工作人员 {staff.name} 信息更新成功', 'success')
    except Exception as e:
        flash(f'更新失败：{str(e)}', 'danger')

    return redirect(url_for('admin.staff_detail', staff_id=staff_id))


@admin_bp.route('/staff/<staff_id>/reset_password')
@login_required
@admin_required
def staff_reset_password(staff_id):
    """重置工作人员密码"""
    staff = Staff.query.get_or_404(staff_id)

    try:
        staff.change_password('123456')
        flash(f'工作人员 {staff.name} 密码已重置为 123456', 'success')
    except Exception as e:
        flash(f'密码重置失败：{str(e)}', 'danger')

    return redirect(url_for('admin.staff_detail', staff_id=staff_id))


@admin_bp.route('/staff/<staff_id>/toggle_status')
@login_required
@admin_required
def staff_toggle_status(staff_id):
    """切换工作人员状态"""
    staff = Staff.query.get_or_404(staff_id)

    try:
        if staff.status == 'active':
            staff.deactivate()
            flash(f'工作人员 {staff.name} 已停用', 'warning')
        else:
            staff.activate()
            flash(f'工作人员 {staff.name} 已激活', 'success')
    except Exception as e:
        flash(f'状态切换失败：{str(e)}', 'danger')

    return redirect(url_for('admin.staff_detail', staff_id=staff_id))


@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """管理员仪表盘"""
    # 获取统计数据
    reader_count = Reader.query.count()
    book_count = Book.query.count()
    loan_count = Loan.query.count()
    overdue_count = Loan.query.filter(
        Loan.return_date.is_(None),
        Loan.due_date < get_current_time()
    ).count()

    # 获取最近借阅
    recent_loans = Loan.query.order_by(Loan.loan_date.desc()).limit(5).all()

    # 获取库存预警
    low_stock_books = Book.query.filter(Book.available_quantity < 3).limit(5).all()

    # 获取借阅趋势数据
    thirty_days_ago = get_current_time() - timedelta(days=30)
    loan_trend = db.session.query(
        func.date(Loan.loan_date).label('date'),
        func.count().label('count')
    ).filter(
        Loan.loan_date >= thirty_days_ago
    ).group_by(
        func.date(Loan.loan_date)
    ).order_by(
        func.date(Loan.loan_date)
    ).all()

    # 获取图书类别统计
    category_stats = db.session.query(
        BookCategory.category_name,
        func.count(Book.isbn).label('book_count')
    ).join(
        Book, Book.book_category_id == BookCategory.book_category_id
    ).group_by(
        BookCategory.category_name
    ).all()

    return render_template(
        'admin/dashboard.html',
        reader_count=reader_count,
        book_count=book_count,
        loan_count=loan_count,
        overdue_count=overdue_count,
        recent_loans=recent_loans,
        low_stock_books=low_stock_books,
        loan_trend=loan_trend,
        category_stats=category_stats
    )


@admin_bp.route('/readers')
@login_required
@admin_required
def readers():
    """读者管理"""
    # 获取搜索参数
    search_query = request.args.get('q', '')
    reader_type = request.args.get('type', '')
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 构建查询
    query = Reader.query

    # 添加搜索条件
    if search_query:
        query = query.filter(
            (Reader.reader_id.like(f'%{search_query}%')) |
            (Reader.name.like(f'%{search_query}%')) |
            (Reader.department.like(f'%{search_query}%')) |
            (Reader.email.like(f'%{search_query}%'))
        )

    if reader_type:
        query = query.filter(Reader.reader_type == reader_type)

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    readers = pagination.items

    # 获取所有读者类型
    reader_types = ReaderType.query.all()

    return render_template(
        'admin/readers.html',
        readers=readers,
        pagination=pagination,
        search_query=search_query,
        reader_type=reader_type,
        reader_types=reader_types
    )


@admin_bp.route('/reader/<string:reader_id>')
@login_required
@admin_required
def reader_detail(reader_id):
    """读者详情"""
    reader = Reader.query.get_or_404(reader_id)
    return render_template('admin/reader_detail.html', reader=reader)


@admin_bp.route('/reader/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_reader():
    """创建读者"""
    if request.method == 'POST':
        reader_id = request.form.get('reader_id')
        name = request.form.get('name')
        department = request.form.get('department')
        phone = request.form.get('phone')
        email = request.form.get('email')
        reader_type = request.form.get('reader_type')
        pwd = request.form.get('pwd', '123456')

        # 验证必填字段
        if not reader_id or not name or not reader_type:
            flash('请填写必填字段', 'danger')
            reader_types = ReaderType.query.all()
            return render_template('admin/create_reader.html', reader_types=reader_types)

        # 检查读者ID是否已存在
        if Reader.query.get(reader_id):
            flash(f'读者ID {reader_id} 已存在', 'danger')
            reader_types = ReaderType.query.all()
            return render_template('admin/create_reader.html', reader_types=reader_types)

        # 创建读者
        reader = Reader(
            reader_id=reader_id,
            name=name,
            department=department,
            phone=phone,
            email=email,
            reader_type=reader_type,
            pwd=pwd
        )

        db.session.add(reader)
        db.session.commit()

        flash('读者创建成功', 'success')
        return redirect(url_for('admin.readers'))

    # GET请求
    reader_types = ReaderType.query.all()
    return render_template('admin/create_reader.html', reader_types=reader_types)


@admin_bp.route('/reader/<string:reader_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_reader(reader_id):
    """编辑读者"""
    reader = Reader.query.get_or_404(reader_id)

    if request.method == 'POST':
        name = request.form.get('name')
        department = request.form.get('department')
        phone = request.form.get('phone')
        email = request.form.get('email')
        reader_type = request.form.get('reader_type')

        # 验证必填字段
        if not name or not reader_type:
            flash('请填写必填字段', 'danger')
            reader_types = ReaderType.query.all()
            return render_template('admin/edit_reader.html', reader=reader, reader_types=reader_types)

        # 更新读者
        reader.name = name
        reader.department = department
        reader.phone = phone
        reader.email = email
        reader.reader_type = reader_type

        db.session.commit()

        flash('读者更新成功', 'success')
        return redirect(url_for('admin.reader_detail', reader_id=reader_id))

    # GET请求
    reader_types = ReaderType.query.all()
    return render_template('admin/edit_reader.html', reader=reader, reader_types=reader_types)


@admin_bp.route('/reader/<string:reader_id>/reset_password', methods=['POST'])
@login_required
@admin_required
def reset_reader_password(reader_id):
    """重置读者密码"""
    reader = Reader.query.get_or_404(reader_id)

    # 重置密码为默认密码
    reader.pwd = '123456'

    db.session.commit()

    flash('密码重置成功', 'success')
    return redirect(url_for('admin.reader_detail', reader_id=reader_id))


# 图书管理相关路由
@admin_bp.route('/books')
@login_required
@admin_required
def books():
    """图书管理"""
    # 获取搜索参数
    search_query = request.args.get('q', '')
    category_id = request.args.get('category', '')
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 构建查询
    query = Book.query

    # 添加搜索条件
    if search_query:
        query = query.filter(
            (Book.isbn.like(f'%{search_query}%')) |
            (Book.title.like(f'%{search_query}%')) |
            (Book.author.like(f'%{search_query}%')) |
            (Book.publisher.like(f'%{search_query}%'))
        )

    if category_id:
        query = query.filter(Book.book_category_id == category_id)

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    books = pagination.items

    # 获取所有图书类别
    categories = BookCategory.query.all()

    return render_template(
        'admin/books.html',
        books=books,
        pagination=pagination,
        search_query=search_query,
        category_id=category_id,
        categories=categories
    )


@admin_bp.route('/book/<string:isbn>')
@login_required
@admin_required
def book_detail(isbn):
    """图书详情"""
    book = Book.query.get_or_404(isbn)
    return render_template('admin/book_detail.html', book=book)


@admin_bp.route('/book/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_book():
    """创建图书"""
    if request.method == 'POST':
        isbn = request.form.get('isbn')
        title = request.form.get('title')
        author = request.form.get('author')
        publisher = request.form.get('publisher')
        year = request.form.get('year')
        book_category_id = request.form.get('book_category_id')
        quantity = request.form.get('quantity', type=int)
        available_quantity = request.form.get('available_quantity', type=int)

        # 验证必填字段
        if not isbn or not title or not author or not publisher or not year or not book_category_id or quantity is None:
            flash('请填写所有必填字段', 'danger')
            categories = BookCategory.query.all()
            return render_template('admin/create_book.html', categories=categories)

        # 检查ISBN是否已存在
        if Book.query.get(isbn):
            flash(f'ISBN {isbn} 已存在', 'danger')
            categories = BookCategory.query.all()
            return render_template('admin/create_book.html', categories=categories)

        # 如果未提供可借数量，默认等于总数量
        if available_quantity is None:
            available_quantity = quantity

        # 创建图书
        book = Book(
            isbn=isbn,
            title=title,
            author=author,
            publisher=publisher,
            year=year,
            book_category_id=book_category_id,
            quantity=quantity,
            available_quantity=available_quantity
        )

        db.session.add(book)
        db.session.commit()

        flash('图书创建成功', 'success')
        return redirect(url_for('admin.books'))

    # GET请求
    categories = BookCategory.query.all()
    return render_template('admin/create_book.html', categories=categories)


@admin_bp.route('/book/<string:isbn>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_book(isbn):
    """编辑图书"""
    book = Book.query.get_or_404(isbn)

    if request.method == 'POST':
        title = request.form.get('title')
        author = request.form.get('author')
        publisher = request.form.get('publisher')
        year = request.form.get('year')
        book_category_id = request.form.get('book_category_id')
        quantity = request.form.get('quantity', type=int)
        available_quantity = request.form.get('available_quantity', type=int)

        # 验证必填字段
        if not title or not author or not publisher or not year or not book_category_id or quantity is None:
            flash('请填写所有必填字段', 'danger')
            categories = BookCategory.query.all()
            return render_template('admin/edit_book.html', book=book, categories=categories)

        # 更新图书
        book.title = title
        book.author = author
        book.publisher = publisher
        book.year = year
        book.book_category_id = book_category_id
        book.quantity = quantity
        book.available_quantity = available_quantity

        db.session.commit()

        flash('图书更新成功', 'success')
        return redirect(url_for('admin.book_detail', isbn=isbn))

    # GET请求
    categories = BookCategory.query.all()
    return render_template('admin/edit_book.html', book=book, categories=categories)


@admin_bp.route('/book/<string:isbn>/delete', methods=['POST'])
@login_required
@admin_required
def delete_book(isbn):
    """删除图书"""
    book = Book.query.get_or_404(isbn)

    # 检查是否有关联的借阅记录
    if book.loans:
        flash('无法删除此图书，因为存在关联的借阅记录', 'danger')
        return redirect(url_for('admin.book_detail', isbn=isbn))

    db.session.delete(book)
    db.session.commit()

    flash('图书删除成功', 'success')
    return redirect(url_for('admin.books'))


# 图书类别管理相关路由
@admin_bp.route('/categories')
@login_required
@admin_required
def categories():
    """图书类别管理"""
    # 获取搜索参数
    search_query = request.args.get('q', '')
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 构建查询
    query = BookCategory.query

    # 添加搜索条件
    if search_query:
        query = query.filter(
            (BookCategory.book_category_id.like(f'%{search_query}%')) |
            (BookCategory.category_name.like(f'%{search_query}%'))
        )

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    categories = pagination.items

    return render_template(
        'admin/categories.html',
        categories=categories,
        pagination=pagination,
        search_query=search_query
    )


@admin_bp.route('/category/<string:category_id>')
@login_required
@admin_required
def category_detail(category_id):
    """图书类别详情"""
    category = BookCategory.query.get_or_404(category_id)

    # 获取该类别下的图书
    books = Book.query.filter_by(book_category_id=category_id).all()

    return render_template(
        'admin/category_detail.html',
        category=category,
        books=books
    )


@admin_bp.route('/category/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_category():
    """创建图书类别"""
    if request.method == 'POST':
        book_category_id = request.form.get('book_category_id')
        category_name = request.form.get('category_name')
        fine_amount = request.form.get('fine_amount', type=float, default=0.1)

        # 验证必填字段
        if not book_category_id or not category_name:
            flash('请填写所有必填字段', 'danger')
            return render_template('admin/create_category.html')

        # 检查类别ID是否已存在
        if BookCategory.query.get(book_category_id):
            flash(f'类别ID {book_category_id} 已存在', 'danger')
            return render_template('admin/create_category.html')

        # 创建图书类别
        category = BookCategory(
            book_category_id=book_category_id,
            category_name=category_name,
            fine_amount=fine_amount
        )

        db.session.add(category)
        db.session.commit()

        flash('图书类别创建成功', 'success')
        return redirect(url_for('admin.categories'))

    # GET请求
    return render_template('admin/create_category.html')


@admin_bp.route('/category/<string:category_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_category(category_id):
    """编辑图书类别"""
    category = BookCategory.query.get_or_404(category_id)

    if request.method == 'POST':
        category_name = request.form.get('category_name')
        fine_amount = request.form.get('fine_amount', type=float)

        # 验证必填字段
        if not category_name:
            flash('请填写类别名称', 'danger')
            return render_template('admin/edit_category.html', category=category)

        # 更新图书类别
        category.category_name = category_name
        if fine_amount is not None:
            category.fine_amount = fine_amount

        db.session.commit()

        flash('图书类别更新成功', 'success')
        return redirect(url_for('admin.category_detail', category_id=category_id))

    # GET请求
    return render_template('admin/edit_category.html', category=category)


@admin_bp.route('/category/<string:category_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_category(category_id):
    """删除图书类别"""
    category = BookCategory.query.get_or_404(category_id)

    # 检查是否有关联的图书
    if category.books:
        flash('无法删除此类别，因为存在关联的图书', 'danger')
        return redirect(url_for('admin.category_detail', category_id=category_id))

    db.session.delete(category)
    db.session.commit()

    flash('图书类别删除成功', 'success')
    return redirect(url_for('admin.categories'))


# 借阅管理相关路由
@admin_bp.route('/loans')
@login_required
@admin_required
def loans():
    """借阅记录管理"""
    # 获取搜索参数
    search_query = request.args.get('q', '')
    status = request.args.get('status', '')
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 构建查询
    query = Loan.query

    # 添加搜索条件
    if search_query:
        query = query.join(Reader).join(Book).filter(
            (Reader.reader_id.like(f'%{search_query}%')) |
            (Reader.name.like(f'%{search_query}%')) |
            (Book.isbn.like(f'%{search_query}%')) |
            (Book.title.like(f'%{search_query}%'))
        )

    # 根据状态筛选
    if status == 'current':
        # 当前借阅（未归还）
        query = query.filter(Loan.return_date.is_(None))
    elif status == 'returned':
        # 已归还
        query = query.filter(Loan.return_date.isnot(None))
    elif status == 'overdue':
        # 逾期未还
        now = get_current_time()
        query = query.filter(
            Loan.return_date.is_(None),
            Loan.due_date < now
        )

    # 分页
    pagination = query.order_by(Loan.loan_date.desc()).paginate(page=page, per_page=per_page, error_out=False)
    loans = pagination.items

    return render_template(
        'admin/loans.html',
        loans=loans,
        pagination=pagination,
        search_query=search_query,
        status=status
    )


@admin_bp.route('/loan/<int:loan_id>')
@login_required
@admin_required
def loan_detail(loan_id):
    """借阅详情"""
    loan = Loan.query.get_or_404(loan_id)
    return render_template('admin/loan_detail.html', loan=loan)


@admin_bp.route('/loan/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_loan():
    """创建借阅记录（借书）"""
    if request.method == 'POST':
        reader_id = request.form.get('reader_id')
        isbn = request.form.get('isbn')

        # 验证必填字段
        if not reader_id or not isbn:
            flash('请填写所有必填字段', 'danger')
            readers = Reader.query.all()
            books = Book.query.filter(Book.available_quantity > 0).all()
            return render_template('admin/create_loan.html', readers=readers, books=books)

        # 获取读者和图书
        reader = Reader.query.get(reader_id)
        book = Book.query.get(isbn)

        # 验证读者和图书是否存在
        if not reader:
            flash(f'读者 {reader_id} 不存在', 'danger')
            readers = Reader.query.all()
            books = Book.query.filter(Book.available_quantity > 0).all()
            return render_template('admin/create_loan.html', readers=readers, books=books)

        if not book:
            flash(f'图书 {isbn} 不存在', 'danger')
            readers = Reader.query.all()
            books = Book.query.filter(Book.available_quantity > 0).all()
            return render_template('admin/create_loan.html', readers=readers, books=books)

        # 检查图书是否可借
        if book.available_quantity <= 0:
            flash(f'图书 {book.title} 已无可借数量', 'danger')
            readers = Reader.query.all()
            books = Book.query.filter(Book.available_quantity > 0).all()
            return render_template('admin/create_loan.html', readers=readers, books=books)

        # 检查读者是否可以借书（是否达到最大借阅数量）
        current_loans_count = Loan.query.filter_by(reader_id=reader_id, return_date=None).count()
        if current_loans_count >= reader.reader_type_info.max_books:
            flash(f'读者 {reader.name} 已达到最大借阅数量 {reader.reader_type_info.max_books}', 'danger')
            readers = Reader.query.all()
            books = Book.query.filter(Book.available_quantity > 0).all()
            return render_template('admin/create_loan.html', readers=readers, books=books)

        # 创建借阅记录
        now = get_current_time()
        borrow_duration = reader.reader_type_info.borrow_duration
        loan = Loan(
            reader_id=reader_id,
            isbn=isbn,
            loan_date=now,
            due_date=now + timedelta(days=borrow_duration)
        )

        # 更新图书可借数量
        book.available_quantity -= 1

        db.session.add(loan)
        db.session.commit()

        flash('借阅记录创建成功', 'success')
        return redirect(url_for('admin.loans'))

    # GET请求
    readers = Reader.query.all()
    books = Book.query.filter(Book.available_quantity > 0).all()
    return render_template('admin/create_loan.html', readers=readers, books=books)


@admin_bp.route('/loan/<int:loan_id>/return', methods=['GET', 'POST'])
@login_required
@admin_required
def return_loan(loan_id):
    """归还图书"""
    loan = Loan.query.get_or_404(loan_id)

    # 检查是否已归还
    if loan.return_date is not None:
        flash('此图书已归还', 'warning')
        return redirect(url_for('admin.loan_detail', loan_id=loan_id))

    if request.method == 'POST':
        # 设置归还日期
        now = get_current_time()
        loan.return_date = now

        # 计算罚款金额（如果逾期）
        if loan.is_overdue():
            fine_amount = loan.calculate_fine()
            loan.fine_amount = fine_amount

        # 更新图书可借数量
        book = loan.book
        book.available_quantity += 1

        db.session.commit()

        flash('图书归还成功', 'success')
        return redirect(url_for('admin.loans'))

    # GET请求
    return render_template('admin/return_loan.html', loan=loan)


@admin_bp.route('/loan/<int:loan_id>/renew', methods=['GET', 'POST'])
@login_required
@admin_required
def renew_loan(loan_id):
    """续借图书"""
    loan = Loan.query.get_or_404(loan_id)

    # 检查是否已归还
    if loan.return_date is not None:
        flash('此图书已归还，不能续借', 'danger')
        return redirect(url_for('admin.loan_detail', loan_id=loan_id))

    # 检查是否逾期
    if loan.is_overdue():
        flash('此图书已逾期，不能续借', 'danger')
        return redirect(url_for('admin.loan_detail', loan_id=loan_id))

    if request.method == 'POST':
        # 获取读者
        reader = loan.reader

        # 获取已续借次数和最大续借次数
        renewal_count = loan.get_renewal_count()
        max_renewals = reader.reader_type_info.max_renewals

        # 检查是否已达到最大续借次数
        if renewal_count >= max_renewals:
            flash(f'续借失败，该读者已达到最大续借次数({max_renewals}次)', 'danger')
            return redirect(url_for('admin.loan_detail', loan_id=loan_id))

        # 获取借阅期限
        borrow_duration = reader.reader_type_info.borrow_duration

        # 确保due_date有时区信息
        due_date = ensure_timezone(loan.due_date)

        # 更新应还日期
        loan.due_date = due_date + timedelta(days=borrow_duration)

        db.session.commit()

        flash('图书续借成功', 'success')
        return redirect(url_for('admin.loans'))

    # GET请求
    now = get_current_time()

    # 确保due_date有时区信息，与now保持一致
    due_date = ensure_timezone(loan.due_date)

    # 计算剩余天数
    remaining_days = (due_date - now).days

    # 计算续借后的应还日期
    borrow_duration = loan.reader.reader_type_info.borrow_duration
    new_due_date = due_date + timedelta(days=borrow_duration)

    return render_template('admin/renew_loan.html', loan=loan, now=now, timedelta=timedelta,
                          remaining_days=remaining_days, new_due_date=new_due_date)



