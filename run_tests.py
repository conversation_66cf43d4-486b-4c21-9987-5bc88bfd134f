#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图书借阅管理系统测试运行脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tests.test_library_system_complete import LibrarySystemTester


async def main():
    """运行测试"""
    print("🚀 启动图书借阅管理系统自动化测试")
    print("请确保:")
    print("1. Flask应用正在运行 (http://localhost:5000)")
    print("2. 数据库已正确配置并包含测试数据")
    print("3. 已安装playwright: pip install playwright")
    print("4. 已安装浏览器: playwright install")
    print()
    
    input("按回车键开始测试...")
    
    tester = LibrarySystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试运行失败: {str(e)}")
        sys.exit(1)
