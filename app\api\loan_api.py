#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
借阅API资源
"""

from datetime import datetime, timedelta
import pytz
from flask import request, current_app
from flask_restful import Resource
from marshmallow import ValidationError

from .. import db
from ..models.loan import Loan
from ..models.reader import Reader
from ..models.book import Book
from ..schemas.loan_schema import LoanSchema, LoanCreateSchema, LoanReturnSchema
from ..utils.db import get_or_404
from ..utils.time_util import get_current_time


class LoanListResource(Resource):
    """借阅列表资源"""

    def get(self):
        """获取所有借阅"""
        loans = Loan.query.all()
        return LoanSchema(many=True).dump(loans)

    def post(self):
        """创建借阅"""
        json_data = request.get_json()
        if not json_data:
            return {'message': '没有提供数据'}, 400

        try:
            loan_data = LoanCreateSchema().load(json_data)
        except ValidationError as err:
            return {'message': '数据验证错误', 'errors': err.messages}, 422

        # 获取读者和图书
        reader = Reader.query.get(loan_data['reader_id'])
        book = Book.query.get(loan_data['isbn'])

        # 检查读者是否可以借书
        if not reader.can_borrow():
            return {'message': f'读者 {reader.reader_id} 已达到最大借阅数量'}, 400

        # 检查图书是否可借
        if not book.is_available():
            return {'message': f'图书 {book.isbn} 已无可借数量'}, 400

        # 获取借阅期限
        borrow_duration = reader.reader_type_info.borrow_duration

        # 创建借阅记录
        now = get_current_time()
        loan = Loan(
            reader_id=reader.reader_id,
            isbn=book.isbn,
            loan_date=now,
            due_date=now + timedelta(days=borrow_duration)
        )

        # 保存借阅记录
        db.session.add(loan)

        # 更新图书可借数量
        book.available_quantity -= 1

        db.session.commit()

        return LoanSchema().dump(loan), 201


class LoanResource(Resource):
    """借阅资源"""

    def get(self, loan_id):
        """获取借阅"""
        loan = get_or_404(Loan, loan_id, f'借阅记录 {loan_id} 不存在')
        return LoanSchema().dump(loan)

    def delete(self, loan_id):
        """删除借阅"""
        loan = get_or_404(Loan, loan_id, f'借阅记录 {loan_id} 不存在')

        # 如果借阅未归还，恢复图书可借数量
        if loan.return_date is None:
            book = Book.query.get(loan.isbn)
            book.available_quantity += 1

        db.session.delete(loan)
        db.session.commit()

        return {'message': f'借阅记录 {loan_id} 已删除'}


class LoanReturnResource(Resource):
    """借阅归还资源"""

    def post(self, loan_id):
        """归还借阅"""
        loan = get_or_404(Loan, loan_id, f'借阅记录 {loan_id} 不存在')

        # 检查是否已归还
        if loan.return_date is not None:
            return {'message': f'借阅记录 {loan_id} 已归还'}, 400

        json_data = request.get_json() or {}

        # 如果没有提供归还日期，使用当前时间
        if 'return_date' not in json_data:
            json_data['return_date'] = get_current_time()

        try:
            data = LoanReturnSchema().load(json_data)
        except ValidationError as err:
            return {'message': '数据验证错误', 'errors': err.messages}, 422

        # 更新归还日期
        loan.return_date = data['return_date']

        # 计算罚款
        if loan.is_overdue():
            fine_amount = loan.calculate_fine()
            loan.fine_amount = fine_amount

        # 更新图书可借数量
        book = Book.query.get(loan.isbn)
        book.available_quantity += 1

        db.session.commit()

        return LoanSchema().dump(loan)


class LoanRenewResource(Resource):
    """借阅续借资源"""

    def post(self, loan_id):
        """续借"""
        loan = get_or_404(Loan, loan_id, f'借阅记录 {loan_id} 不存在')

        # 检查是否已归还
        if loan.return_date is not None:
            return {'message': f'借阅记录 {loan_id} 已归还，不能续借'}, 400

        # 检查是否逾期
        if loan.is_overdue():
            return {'message': f'借阅记录 {loan_id} 已逾期，不能续借'}, 400

        # 获取读者
        reader = Reader.query.get(loan.reader_id)

        # 获取已续借次数和最大续借次数
        renewal_count = loan.get_renewal_count()
        max_renewals = reader.reader_type_info.max_renewals

        # 检查是否已达到最大续借次数
        if renewal_count >= max_renewals:
            return {'message': f'借阅记录 {loan_id} 已达到最大续借次数({max_renewals}次)'}, 400

        # 续借
        if not loan.renew(reader):
            return {'message': f'借阅记录 {loan_id} 不能续借'}, 400

        db.session.commit()

        return LoanSchema().dump(loan)
