#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图书相关数据验证和序列化模式
"""

from marshmallow import fields, validate, validates, ValidationError

from .. import ma
from ..models.book import Book, BookCategory


class BookCategorySchema(ma.SQLAlchemyAutoSchema):
    """图书类别模式"""
    class Meta:
        model = BookCategory
        load_instance = True
    
    book_category_id = fields.String(required=True, validate=validate.Length(min=1, max=10))
    category_name = fields.String(required=True, validate=validate.Length(min=1, max=50))
    fine_amount = fields.Float(validate=validate.Range(min=0))


class BookSchema(ma.SQLAlchemyAutoSchema):
    """图书模式"""
    class Meta:
        model = Book
        load_instance = True
        include_fk = True
    
    isbn = fields.String(required=True, validate=validate.Length(min=1, max=20))
    title = fields.String(required=True, validate=validate.Length(min=1, max=255))
    author = fields.String(required=True, validate=validate.Length(min=1, max=100))
    publisher = fields.String(required=True, validate=validate.Length(min=1, max=100))
    year = fields.Integer(required=True, validate=validate.Range(min=1000, max=9999))
    book_category_id = fields.String(required=True, validate=validate.Length(min=1, max=10))
    quantity = fields.Integer(required=True, validate=validate.Range(min=0))
    available_quantity = fields.Integer(validate=validate.Range(min=0))
    
    @validates('book_category_id')
    def validate_book_category_id(self, value):
        """验证图书类别是否存在"""
        book_category = BookCategory.query.get(value)
        if not book_category:
            raise ValidationError(f'图书类别 {value} 不存在')
    
    @validates('available_quantity')
    def validate_available_quantity(self, value, **kwargs):
        """验证可借数量不超过总数量"""
        if 'quantity' in kwargs['data'] and value > kwargs['data']['quantity']:
            raise ValidationError('可借数量不能超过总数量')


class BookSearchSchema(ma.Schema):
    """图书搜索模式"""
    isbn = fields.String(validate=validate.Length(max=20))
    title = fields.String(validate=validate.Length(max=255))
    author = fields.String(validate=validate.Length(max=100))
    publisher = fields.String(validate=validate.Length(max=100))
    year = fields.Integer(validate=validate.Range(min=1000, max=9999))
    book_category_id = fields.String(validate=validate.Length(max=10))
    page = fields.Integer(validate=validate.Range(min=1), missing=1)
    per_page = fields.Integer(validate=validate.Range(min=1, max=100), missing=10)
