{"processing": "处理中...", "lengthMenu": "显示 _MENU_ 项结果", "zeroRecords": "没有匹配结果", "info": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项", "infoEmpty": "显示第 0 至 0 项结果，共 0 项", "infoFiltered": "(由 _MAX_ 项结果过滤)", "search": "搜索:", "emptyTable": "表中数据为空", "paginate": {"first": "首页", "previous": "上页", "next": "下页", "last": "末页"}, "aria": {"sortAscending": ": 以升序排列此列", "sortDescending": ": 以降序排列此列"}, "autoFill": {"cancel": "取消", "fill": "用 <i>%d</i> 填充所有单元格", "fillHorizontal": "水平填充单元格", "fillVertical": "垂直填充单元格"}, "buttons": {"collection": "集合 <span class='ui-button-icon-primary ui-icon ui-icon-triangle-1-s'></span>", "colvis": "列可见性", "colvisRestore": "恢复列可见性", "copy": "复制", "copyKeys": "按 ctrl 或 u2318 + C 将表数据复制到系统剪贴板。<br><br>要取消，请单击此消息或按Escape键。", "copySuccess": {"1": "复制了 1 行到剪贴板", "_": "复制了 %d 行到剪贴板"}, "copyTitle": "复制到剪贴板", "csv": "CSV", "excel": "Excel", "pageLength": {"-1": "显示所有行", "_": "显示 %d 行"}, "pdf": "PDF", "print": "打印"}, "searchBuilder": {"add": "添加条件", "button": {"0": "搜索生成器", "_": "搜索生成器 (%d)"}, "clearAll": "清除所有", "condition": "条件", "conditions": {"date": {"after": "之后", "before": "之前", "between": "之间", "empty": "为空", "equals": "等于", "not": "不等于", "notBetween": "不在之间", "notEmpty": "不为空"}, "number": {"between": "之间", "empty": "为空", "equals": "等于", "gt": "大于", "gte": "大于等于", "lt": "小于", "lte": "小于等于", "not": "不等于", "notBetween": "不在之间", "notEmpty": "不为空"}, "string": {"contains": "包含", "empty": "为空", "endsWith": "结尾是", "equals": "等于", "not": "不等于", "notEmpty": "不为空", "startsWith": "开头是"}}, "data": "数据", "deleteTitle": "删除过滤规则", "leftTitle": "向左缩进", "logicAnd": "与", "logicOr": "或", "rightTitle": "向右缩进", "title": {"0": "搜索生成器", "_": "搜索生成器 (%d)"}, "value": "值"}, "searchPanes": {"clearMessage": "清除所有", "collapse": {"0": "搜索面板", "_": "搜索面板 (%d)"}, "count": "{total}", "countFiltered": "{shown} ({total})", "emptyPanes": "没有搜索面板", "loadMessage": "正在加载搜索面板...", "title": "激活的筛选条件 - %d"}, "select": {"cells": {"1": "选择了1个单元格", "_": "选择了%d个单元格"}, "columns": {"1": "选择了1列", "_": "选择了%d列"}, "rows": {"1": "选择了1行", "_": "选择了%d行"}}}