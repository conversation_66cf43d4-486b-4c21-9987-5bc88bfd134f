<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="650" height="550" viewBox="0 0 650 550" preserveAspectRatio="xMidYMid slice">
  <!-- 定义渐变和滤镜 -->
  <defs>
    <!-- 白色渐变 -->
    <linearGradient id="whiteGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" />
      <stop offset="100%" stop-color="#f8f9fa" />
    </linearGradient>
    
    <!-- 彩色渐变系列 - 更鲜艳的颜色 -->
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffcdd2" />
      <stop offset="100%" stop-color="#ef9a9a" />
    </linearGradient>
    
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffe0b2" />
      <stop offset="100%" stop-color="#ffcc80" />
    </linearGradient>
    
    <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#fff9c4" />
      <stop offset="100%" stop-color="#fff59d" />
    </linearGradient>
    
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#c8e6c9" />
      <stop offset="100%" stop-color="#a5d6a7" />
    </linearGradient>
    
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#bbdefb" />
      <stop offset="100%" stop-color="#90caf9" />
    </linearGradient>
    
    <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#e1bee7" />
      <stop offset="100%" stop-color="#ce93d8" />
    </linearGradient>
    
    <linearGradient id="pinkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f8bbd0" />
      <stop offset="100%" stop-color="#f48fb1" />
    </linearGradient>
    
    <linearGradient id="cyanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#b2ebf2" />
      <stop offset="100%" stop-color="#80deea" />
    </linearGradient>
    
    <!-- 阴影效果 -->
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3" />
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="4" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- 透明背景 -->
  
  <!-- 中央大型开放书本 -->
  <g transform="translate(325, 275)" filter="url(#shadow)">
    <!-- 左页 -->
    <path d="M-120,-90 C-150,-80 -170,-40 -170,0 C-170,40 -150,80 -120,90 L-10,90 L-10,-90 Z" 
          fill="url(#whiteGradient)" stroke="#ffffff" stroke-width="2" />
    
    <!-- 右页 -->
    <path d="M10,-90 C40,-80 60,-40 60,0 C60,40 40,80 10,90 L-10,90 L-10,-90 Z" 
          fill="url(#whiteGradient)" stroke="#ffffff" stroke-width="2" />
    
    <!-- 书脊 -->
    <rect x="-12" y="-90" width="4" height="180" fill="#ffffff" opacity="0.8" />
    
    <!-- 页面线条 - 左页 -->
    <g stroke="#ffffff" stroke-width="1" opacity="0.8">
      <line x1="-110" y1="-70" x2="-30" y2="-70" />
      <line x1="-110" y1="-50" x2="-30" y2="-50" />
      <line x1="-110" y1="-30" x2="-30" y2="-30" />
      <line x1="-110" y1="-10" x2="-30" y2="-10" />
      <line x1="-110" y1="10" x2="-30" y2="10" />
      <line x1="-110" y1="30" x2="-30" y2="30" />
      <line x1="-110" y1="50" x2="-30" y2="50" />
      <line x1="-110" y1="70" x2="-30" y2="70" />
    </g>
    
    <!-- 页面线条 - 右页 -->
    <g stroke="#ffffff" stroke-width="1" opacity="0.8">
      <line x1="10" y1="-70" x2="90" y2="-70" />
      <line x1="10" y1="-50" x2="90" y2="-50" />
      <line x1="10" y1="-30" x2="90" y2="-30" />
      <line x1="10" y1="-10" x2="90" y2="-10" />
      <line x1="10" y1="10" x2="90" y2="10" />
      <line x1="10" y1="30" x2="90" y2="30" />
      <line x1="10" y1="50" x2="90" y2="50" />
      <line x1="10" y1="70" x2="90" y2="70" />
    </g>
  </g>
  
  <!-- 彩色书籍 - 左侧 -->
  <g transform="translate(120, 275)" filter="url(#shadow)">
    <!-- 书本1 -->
    <rect x="-40" y="-120" width="25" height="150" rx="3" fill="url(#redGradient)" stroke="#ffffff" stroke-width="2" />
    
    <!-- 书本2 -->
    <rect x="-10" y="-130" width="25" height="160" rx="3" fill="url(#orangeGradient)" stroke="#ffffff" stroke-width="2" />
    
    <!-- 书本3 -->
    <rect x="20" y="-125" width="25" height="155" rx="3" fill="url(#yellowGradient)" stroke="#ffffff" stroke-width="2" />
    
    <!-- 书架 -->
    <rect x="-50" y="30" width="105" height="10" rx="2" fill="url(#whiteGradient)" stroke="#ffffff" stroke-width="2" />
  </g>
  
  <!-- 彩色书籍 - 右侧 -->
  <g transform="translate(530, 275)" filter="url(#shadow)">
    <!-- 书本1 -->
    <rect x="-40" y="-125" width="25" height="155" rx="3" fill="url(#greenGradient)" stroke="#ffffff" stroke-width="2" />
    
    <!-- 书本2 -->
    <rect x="-10" y="-130" width="25" height="160" rx="3" fill="url(#blueGradient)" stroke="#ffffff" stroke-width="2" />
    
    <!-- 书本3 -->
    <rect x="20" y="-120" width="25" height="150" rx="3" fill="url(#purpleGradient)" stroke="#ffffff" stroke-width="2" />
    
    <!-- 书架 -->
    <rect x="-50" y="30" width="105" height="10" rx="2" fill="url(#whiteGradient)" stroke="#ffffff" stroke-width="2" />
  </g>
  
  <!-- 顶部知识符号 -->
  <g transform="translate(325, 120)" filter="url(#shadow)">
    <!-- 毕业帽 -->
    <path d="M-60,-15 L0,-50 L60,-15 L0,20 Z" fill="url(#whiteGradient)" stroke="#ffffff" stroke-width="3" />
    <path d="M0,20 L0,60" stroke="#ffffff" stroke-width="4" />
    
    <!-- 光环 -->
    <circle cx="0" cy="-15" r="70" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.7" />
  </g>
  
  <!-- 飘浮的彩色书本 -->
  <g>
    <!-- 左上方书本 -->
    <g transform="translate(180, 150)" filter="url(#shadow)">
      <rect x="-25" y="-20" width="50" height="40" rx="3" fill="url(#pinkGradient)" stroke="#ffffff" stroke-width="1.5" />
    </g>
    
    <!-- 右上方书本 -->
    <g transform="translate(470, 150)" filter="url(#shadow)">
      <rect x="-25" y="-20" width="50" height="40" rx="3" fill="url(#cyanGradient)" stroke="#ffffff" stroke-width="1.5" />
    </g>
    
    <!-- 左下方书本 -->
    <g transform="translate(200, 400)" filter="url(#shadow)">
      <rect x="-20" y="-15" width="40" height="30" rx="3" fill="url(#greenGradient)" stroke="#ffffff" stroke-width="1.5" />
    </g>
    
    <!-- 右下方书本 -->
    <g transform="translate(450, 400)" filter="url(#shadow)">
      <rect x="-20" y="-15" width="40" height="30" rx="3" fill="url(#orangeGradient)" stroke="#ffffff" stroke-width="1.5" />
    </g>
  </g>
  
  <!-- 装饰性光环 -->
  <g opacity="0.7" filter="url(#glow)">
    <circle cx="325" cy="275" r="250" fill="none" stroke="#ffffff" stroke-width="1.5" />
    <circle cx="325" cy="275" r="260" fill="none" stroke="#ffffff" stroke-width="1" stroke-dasharray="5,5" />
    <circle cx="325" cy="275" r="270" fill="none" stroke="#ffffff" stroke-width="0.8" stroke-dasharray="3,3" />
  </g>
  
  <!-- 装饰性光点 -->
  <g fill="#ffffff">
    <circle cx="150" cy="120" r="3" opacity="0.9" />
    <circle cx="200" cy="90" r="2.5" opacity="0.8" />
    <circle cx="450" cy="100" r="3" opacity="0.9" />
    <circle cx="500" cy="130" r="2.5" opacity="0.8" />
    <circle cx="130" cy="430" r="2.5" opacity="0.8" />
    <circle cx="180" cy="460" r="3" opacity="0.9" />
    <circle cx="470" cy="440" r="2.5" opacity="0.8" />
    <circle cx="520" cy="410" r="3" opacity="0.9" />
    
    <circle cx="250" cy="200" r="2" opacity="0.7" />
    <circle cx="400" cy="200" r="2" opacity="0.7" />
    <circle cx="250" cy="350" r="2" opacity="0.7" />
    <circle cx="400" cy="350" r="2" opacity="0.7" />
  </g>
  
  <!-- 标题 -->
  <g transform="translate(325, 480)" filter="url(#shadow)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="36" font-weight="bold" text-anchor="middle" fill="#ffffff">图书借阅管理系统</text>
    <text x="0" y="40" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="#ffffff">知识的殿堂，智慧的源泉</text>
  </g>
</svg>
