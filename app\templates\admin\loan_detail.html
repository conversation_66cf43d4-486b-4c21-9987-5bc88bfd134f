{% extends 'base.html' %}

{% block title %}借阅详情 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
<style>
    /* 借阅详情页面特定样式 */
    .loan-status-card {
        border-radius: 1rem;
        background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
        color: white;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .loan-status-card:hover {
        transform: translateY(-5px);
    }

    .loan-status-icon {
        font-size: 2.5rem;
        opacity: 0.2;
        position: absolute;
        top: 1rem;
        right: 1.5rem;
    }

    .loan-info-table th {
        width: 25%;
        background-color: rgba(66, 153, 225, 0.05);
        font-weight: 600;
    }

    .loan-info-table td {
        vertical-align: middle;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 50rem;
        font-weight: 500;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .badge-success {
        background-color: rgba(72, 187, 120, 0.1);
        color: #48bb78;
        border: 1px solid rgba(72, 187, 120, 0.2);
    }

    .badge-danger {
        background-color: rgba(245, 101, 101, 0.1);
        color: #f56565;
        border: 1px solid rgba(245, 101, 101, 0.2);
    }

    .badge-primary {
        background-color: rgba(66, 153, 225, 0.1);
        color: #4299e1;
        border: 1px solid rgba(66, 153, 225, 0.2);
    }

    .avatar-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .info-card {
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .info-card .card-header {
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        background: linear-gradient(to right, rgba(26, 58, 95, 0.05), rgba(66, 153, 225, 0.05));
    }

    .action-buttons .btn {
        margin-left: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .action-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    @media (max-width: 992px) {
        .loan-status-card {
            margin-bottom: 1rem;
        }

        .info-card {
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 顶部状态卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="loan-status-card p-4 position-relative zoom-in">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-2">借阅详情 #{{ loan.loan_id }}</h4>
                        <p class="mb-0">{{ loan.book.title }} - {{ loan.reader.name }}</p>
                    </div>
                    <div class="action-buttons">
                        <a href="{{ url_for('admin.loans') }}" class="btn btn-light">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                        {% if not loan.return_date %}
                        <a href="{{ url_for('admin.return_loan', loan_id=loan.loan_id) }}" class="btn btn-success">
                            <i class="bi bi-journal-check"></i> 归还图书
                        </a>
                        {% if not loan.is_overdue() %}
                        {% set renewal_count = loan.get_renewal_count() %}
                        {% set max_renewals = loan.reader.reader_type_info.max_renewals %}
                        <a href="{{ url_for('admin.renew_loan', loan_id=loan.loan_id) }}"
                           class="btn btn-primary {% if renewal_count >= max_renewals %}disabled{% endif %}"
                           {% if renewal_count >= max_renewals %}title="已达到最大续借次数"{% endif %}>
                            <i class="bi bi-arrow-repeat"></i> 续借图书
                        </a>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>

                <div class="mt-3 d-flex align-items-center">
                    <div class="me-4">
                        <span class="d-block text-white-50">状态</span>
                        {% if loan.return_date %}
                        <span class="badge bg-success rounded-pill">
                            <i class="bi bi-check-circle me-1"></i> 已归还
                        </span>
                        {% elif loan.is_overdue() %}
                        <span class="badge bg-danger rounded-pill">
                            <i class="bi bi-exclamation-triangle me-1"></i> 已逾期
                        </span>
                        {% else %}
                        <span class="badge bg-primary rounded-pill">
                            <i class="bi bi-clock me-1"></i> 借阅中
                        </span>
                        {% endif %}
                    </div>

                    {% if loan.is_overdue() and not loan.return_date %}
                    <div class="me-4">
                        <span class="d-block text-white-50">逾期天数</span>
                        <span class="badge bg-danger rounded-pill">{{ loan.get_overdue_days() }} 天</span>
                    </div>
                    {% endif %}

                    <div>
                        <span class="d-block text-white-50">罚款金额</span>
                        {% if loan.fine_amount %}
                        <span class="badge bg-warning rounded-pill text-dark">{{ loan.fine_amount }} 元</span>
                        {% elif loan.is_overdue() %}
                        <span class="badge bg-warning rounded-pill text-dark">{{ loan.calculate_fine() }} 元 (未结算)</span>
                        {% else %}
                        <span class="badge bg-success rounded-pill">0 元</span>
                        {% endif %}
                    </div>
                </div>

                {% if loan.return_date %}
                <i class="bi bi-check-circle-fill loan-status-icon"></i>
                {% elif loan.is_overdue() %}
                <i class="bi bi-exclamation-triangle-fill loan-status-icon"></i>
                {% else %}
                <i class="bi bi-book-fill loan-status-icon"></i>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- 借阅信息卡片 -->
            <div class="info-card shadow mb-4 fade-in">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-journal-text me-2"></i>借阅详细信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table loan-info-table">
                            <tbody>
                                <tr>
                                    <th>借阅ID</th>
                                    <td>{{ loan.loan_id }}</td>
                                </tr>
                                <tr>
                                    <th>读者</th>
                                    <td>
                                        <a href="{{ url_for('admin.reader_detail', reader_id=loan.reader_id) }}" class="d-flex align-items-center text-decoration-none">
                                            <div class="avatar-circle bg-primary text-white me-2" style="width: 30px; height: 30px; font-size: 1rem;">
                                                {{ loan.reader.name[0] }}
                                            </div>
                                            <span>{{ loan.reader.name }} ({{ loan.reader_id }})</span>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>图书</th>
                                    <td>
                                        <a href="{{ url_for('admin.book_detail', isbn=loan.isbn) }}" class="d-flex align-items-center text-decoration-none">
                                            <i class="bi bi-book me-2 text-primary"></i>
                                            <span>{{ loan.book.title }} ({{ loan.isbn }})</span>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>借阅日期</th>
                                    <td>
                                        <i class="bi bi-calendar-date me-2 text-muted"></i>
                                        {{ loan.loan_date|datetime('%Y-%m-%d %H:%M:%S') }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>应还日期</th>
                                    <td>
                                        <i class="bi bi-calendar-check me-2 text-muted"></i>
                                        {{ loan.due_date|datetime('%Y-%m-%d %H:%M:%S') }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>归还日期</th>
                                    <td>
                                        <i class="bi bi-calendar-check-fill me-2 text-muted"></i>
                                        {% if loan.return_date %}
                                        {{ loan.return_date|datetime('%Y-%m-%d %H:%M:%S') }}
                                        {% else %}
                                        <span class="text-muted">尚未归还</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>续借次数</th>
                                    <td>
                                        {% set renewal_count = loan.get_renewal_count() %}
                                        {% set max_renewals = loan.reader.reader_type_info.max_renewals %}
                                        <span class="badge {% if renewal_count >= max_renewals %}bg-danger{% elif renewal_count > 0 %}bg-info{% else %}bg-secondary{% endif %} me-2">
                                            {{ renewal_count }}/{{ max_renewals }}
                                        </span>
                                        {% if renewal_count >= max_renewals %}
                                        <small class="text-danger">(已达到最大续借次数)</small>
                                        {% elif renewal_count > 0 %}
                                        <small class="text-info">(已续借{{ renewal_count }}次)</small>
                                        {% else %}
                                        <small class="text-muted">(未续借)</small>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if loan.return_date %}
                                        <span class="status-badge badge-success">
                                            <i class="bi bi-check-circle me-2"></i> 已归还
                                        </span>
                                        {% elif loan.is_overdue() %}
                                        <span class="status-badge badge-danger">
                                            <i class="bi bi-exclamation-triangle me-2"></i> 已逾期
                                        </span>
                                        {% else %}
                                        <span class="status-badge badge-primary">
                                            <i class="bi bi-clock me-2"></i> 借阅中
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>罚款金额</th>
                                    <td>
                                        {% if loan.fine_amount %}
                                        <span class="text-danger fw-bold">{{ loan.fine_amount }} 元</span>
                                        {% elif loan.is_overdue() %}
                                        <span class="text-danger fw-bold">{{ loan.calculate_fine() }} 元</span>
                                        <small class="text-muted">(尚未结算)</small>
                                        {% else %}
                                        <span class="text-success fw-bold">0 元</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 读者信息卡片 -->
            <div class="info-card shadow mb-4 fade-in">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-person me-2"></i>读者信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="avatar-circle bg-primary text-white me-3">
                            {{ loan.reader.name[0] }}
                        </div>
                        <div>
                            <h5 class="mb-1">{{ loan.reader.name }}</h5>
                            <p class="mb-0 text-muted">
                                <i class="bi bi-person-badge me-1"></i>
                                {{ loan.reader.reader_type }}
                            </p>
                        </div>
                    </div>
                    <div class="mb-4">
                        <p class="mb-2 d-flex align-items-center">
                            <i class="bi bi-person-vcard me-2 text-primary"></i>
                            <strong>读者ID:</strong>
                            <span class="ms-2">{{ loan.reader.reader_id }}</span>
                        </p>
                        <p class="mb-2 d-flex align-items-center">
                            <i class="bi bi-building me-2 text-primary"></i>
                            <strong>学院/部门:</strong>
                            <span class="ms-2">{{ loan.reader.department }}</span>
                        </p>
                        <p class="mb-2 d-flex align-items-center">
                            <i class="bi bi-telephone me-2 text-primary"></i>
                            <strong>电话:</strong>
                            <span class="ms-2">{{ loan.reader.phone }}</span>
                        </p>
                        <p class="mb-0 d-flex align-items-center">
                            <i class="bi bi-envelope me-2 text-primary"></i>
                            <strong>邮箱:</strong>
                            <span class="ms-2">{{ loan.reader.email }}</span>
                        </p>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('admin.reader_detail', reader_id=loan.reader_id) }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-person-lines-fill me-2"></i> 查看读者详情
                        </a>
                    </div>
                </div>
            </div>

            <!-- 图书信息卡片 -->
            <div class="info-card shadow mb-4 fade-in">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-book me-2"></i>图书信息
                    </h6>
                </div>
                <div class="card-body">
                    <h5 class="mb-2 fw-bold">{{ loan.book.title }}</h5>
                    <p class="mb-4 text-muted">
                        <i class="bi bi-person me-1"></i>
                        {{ loan.book.author }}
                    </p>
                    <div class="mb-4">
                        <p class="mb-2 d-flex align-items-center">
                            <i class="bi bi-upc me-2 text-primary"></i>
                            <strong>ISBN:</strong>
                            <span class="ms-2">{{ loan.book.isbn }}</span>
                        </p>
                        <p class="mb-2 d-flex align-items-center">
                            <i class="bi bi-building me-2 text-primary"></i>
                            <strong>出版社:</strong>
                            <span class="ms-2">{{ loan.book.publisher }}</span>
                        </p>
                        <p class="mb-2 d-flex align-items-center">
                            <i class="bi bi-calendar-event me-2 text-primary"></i>
                            <strong>出版年份:</strong>
                            <span class="ms-2">{{ loan.book.year }}</span>
                        </p>
                        <p class="mb-2 d-flex align-items-center">
                            <i class="bi bi-tag me-2 text-primary"></i>
                            <strong>类别:</strong>
                            <span class="ms-2">{{ loan.book.category.category_name }}</span>
                        </p>
                        <p class="mb-0 d-flex align-items-center">
                            <i class="bi bi-journals me-2 text-primary"></i>
                            <strong>可借数量:</strong>
                            <span class="ms-2">
                                {% if loan.book.available_quantity > 0 %}
                                <span class="badge bg-success rounded-pill">{{ loan.book.available_quantity }}</span>
                                {% else %}
                                <span class="badge bg-danger rounded-pill">0</span>
                                {% endif %}
                            </span>
                        </p>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('admin.book_detail', isbn=loan.isbn) }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-book-half me-2"></i> 查看图书详情
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
