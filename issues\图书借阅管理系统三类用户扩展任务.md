# 图书借阅管理系统三类用户扩展任务

## 任务概述
扩展现有图书借阅管理系统，实现三类用户（系统管理员、读者、工作人员）的完整功能，满足图书馆日常管理需求。

## 需求分析
- **系统管理员**：管理读者、图书、工作人员信息的CRUD操作
- **读者**：借书、还书、查询图书信息、查询借阅记录
- **工作人员**：办理借书、还书、处理超期罚款

## 技术方案
采用渐进式扩展现有系统，保持现有功能稳定，新增工作人员角色和相关功能。

## 执行计划

### 阶段一：数据库扩展（优先级：高）

#### 步骤1.1：创建工作人员表
- **文件路径**：`database_updates.sql`
- **操作内容**：创建Staff表结构
- **预期结果**：工作人员数据表就绪
- **依赖**：无

#### 步骤1.2：扩展读者表
- **文件路径**：`database_updates.sql`
- **操作内容**：为Reader表添加Status字段
- **预期结果**：支持证件状态管理
- **依赖**：步骤1.1完成

#### 步骤1.3：完善借阅表
- **文件路径**：`database_updates.sql`
- **操作内容**：优化Loan表的罚款计算逻辑
- **预期结果**：支持精确的罚款计算
- **依赖**：步骤1.2完成

### 阶段二：模型层扩展（优先级：高）

#### 步骤2.1：创建工作人员模型
- **文件路径**：`app/models/staff.py`
- **操作内容**：创建Staff模型类，包含认证方法
- **涉及类/方法**：Staff类、verify_password方法、get_id方法
- **预期结果**：工作人员数据模型就绪
- **依赖**：步骤1.1完成

#### 步骤2.2：扩展读者模型
- **文件路径**：`app/models/reader.py`
- **操作内容**：添加证件状态相关方法
- **涉及类/方法**：Reader类、is_valid方法、set_status方法
- **预期结果**：支持证件状态管理
- **依赖**：步骤1.2完成

#### 步骤2.3：完善借阅模型
- **文件路径**：`app/models/loan.py`
- **操作内容**：添加罚款计算方法
- **涉及类/方法**：Loan类、calculate_fine方法、get_overdue_days方法
- **预期结果**：自动计算超期罚款
- **依赖**：步骤1.3完成

### 阶段三：认证系统扩展（优先级：高）

#### 步骤3.1：扩展用户加载器
- **文件路径**：`app/models/reader.py`
- **操作内容**：修改load_user函数支持工作人员
- **涉及方法**：load_user函数
- **预期结果**：三类用户统一认证
- **依赖**：步骤2.1完成

#### 步骤3.2：添加工作人员权限装饰器
- **文件路径**：`app/utils/auth.py`
- **操作内容**：创建staff_required装饰器
- **涉及方法**：staff_required函数
- **预期结果**：工作人员权限控制
- **依赖**：步骤3.1完成

#### 步骤3.3：扩展登录逻辑
- **文件路径**：`app/views/auth.py`
- **操作内容**：修改login函数支持工作人员登录
- **涉及方法**：login函数
- **预期结果**：三类用户可以登录
- **依赖**：步骤3.2完成

### 阶段四：视图层扩展（优先级：中）

#### 步骤4.1：创建工作人员视图
- **文件路径**：`app/views/staff.py`
- **操作内容**：创建工作人员蓝图和基础视图
- **涉及方法**：dashboard、borrow_book、return_book、fine_management
- **预期结果**：工作人员操作界面
- **依赖**：步骤3.3完成

#### 步骤4.2：扩展管理员视图
- **文件路径**：`app/views/admin.py`
- **操作内容**：添加工作人员管理功能
- **涉及方法**：staff_list、staff_add、staff_edit、staff_delete
- **预期结果**：管理员可以管理工作人员
- **依赖**：步骤4.1完成

#### 步骤4.3：完善借阅管理
- **文件路径**：`app/views/admin.py`, `app/views/staff.py`
- **操作内容**：添加罚款管理功能
- **涉及方法**：fine_list、fine_process、overdue_report
- **预期结果**：完整的罚款管理
- **依赖**：步骤4.2完成

### 阶段五：前端界面开发（优先级：中）

#### 步骤5.1：修改登录页面
- **文件路径**：`app/templates/auth/login.html`
- **操作内容**：添加工作人员登录选项
- **预期结果**：支持三类用户登录
- **依赖**：步骤3.3完成

#### 步骤5.2：创建工作人员界面
- **文件路径**：`app/templates/staff/`目录下的模板文件
- **操作内容**：创建工作人员操作界面
- **预期结果**：工作人员可以进行日常操作
- **依赖**：步骤4.1完成

#### 步骤5.3：扩展管理员界面
- **文件路径**：`app/templates/admin/`目录下的模板文件
- **操作内容**：添加工作人员管理和罚款管理界面
- **预期结果**：管理员功能完整
- **依赖**：步骤4.2完成

### 阶段六：数据验证和API（优先级：低）

#### 步骤6.1：创建工作人员数据验证
- **文件路径**：`app/schemas/staff_schema.py`
- **操作内容**：创建工作人员数据验证模式
- **预期结果**：数据输入验证
- **依赖**：步骤2.1完成

#### 步骤6.2：扩展API接口
- **文件路径**：`app/api/staff_api.py`
- **操作内容**：创建工作人员相关API
- **预期结果**：支持API操作
- **依赖**：步骤6.1完成

### 阶段七：测试和优化（优先级：中）

#### 步骤7.1：功能测试
- **操作内容**：测试三类用户的所有功能
- **预期结果**：功能正常运行
- **依赖**：所有开发步骤完成

#### 步骤7.2：性能优化
- **操作内容**：优化数据库查询和页面加载
- **预期结果**：系统性能良好
- **依赖**：步骤7.1完成

## 风险评估
- **低风险**：数据库扩展、模型创建
- **中风险**：认证系统修改、视图扩展
- **高风险**：用户加载器修改（需要仔细测试）

## 预计工作量
- **总工作量**：3-5个工作日
- **核心功能**：2-3天
- **界面开发**：1-2天
- **测试优化**：1天

## 成功标准
1. 三类用户可以正常登录和使用各自功能
2. 工作人员可以办理借书、还书、处理罚款
3. 管理员可以管理工作人员信息
4. 读者证件状态可以控制借阅权限
5. 超期罚款自动计算（每天0.5元）
6. 借阅期限为一个月
7. 所有现有功能保持正常运行
