{% extends 'base.html' %}

{% block title %}借阅管理 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid px-3 py-2 dashboard-optimized">
    <!-- 搜索卡片 -->
    <div class="chart-card mb-3 zoom-in">
        <div class="card-header">
            <h6 class="card-title">
                <i class="bi bi-search"></i>
                借阅记录搜索
            </h6>
            <div class="card-actions">
                <a href="{{ url_for('admin.create_loan') }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-journal-plus"></i> 借书处理
                </a>
            </div>
        </div>
        <div class="card-body">
            <form action="{{ url_for('admin.loans') }}" method="get" class="search-form">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-search text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 ps-0"
                                placeholder="输入读者ID、姓名、ISBN或书名..."
                                name="q" value="{{ search_query }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" name="status">
                            <option value="">所有状态</option>
                            <option value="current" {% if status == 'current' %}selected{% endif %}>当前借阅</option>
                            <option value="returned" {% if status == 'returned' %}selected{% endif %}>已归还</option>
                            <option value="overdue" {% if status == 'overdue' %}selected{% endif %}>逾期未还</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 借阅记录列表 -->
    <div class="chart-card mb-3 fade-in">
        <div class="card-header">
            <h6 class="card-title">
                <i class="bi bi-journal-text"></i>
                借阅记录列表
            </h6>
            <div class="card-actions">
                <span class="badge bg-primary rounded-pill">
                    共 {{ pagination.total }} 条记录
                </span>
            </div>
        </div>
        <div class="card-body p-0">
            {% if loans %}
            <div class="table-responsive">
                <table class="table table-admin table-hover mb-0" id="loansTable">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="id">
                                借阅ID <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="reader">
                                读者 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="book">
                                图书 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="loan_date">
                                借阅日期 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="due_date">
                                应还日期 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="return_date">
                                归还日期 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="renewal">
                                续借次数 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="status">
                                状态 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for loan in loans %}
                        <tr class="loan-row">
                            <td>
                                <span class="text-monospace">{{ loan.loan_id }}</span>
                            </td>
                            <td>
                                <a href="{{ url_for('admin.reader_detail', reader_id=loan.reader_id) }}">
                                    {{ loan.reader.name }}
                                </a>
                            </td>
                            <td>
                                <a href="{{ url_for('admin.book_detail', isbn=loan.isbn) }}">
                                    {{ loan.book.title }}
                                </a>
                            </td>
                            <td>{{ loan.loan_date|datetime('%Y-%m-%d') }}</td>
                            <td>{{ loan.due_date|datetime('%Y-%m-%d') }}</td>
                            <td>
                                {% if loan.return_date %}
                                {{ loan.return_date|datetime('%Y-%m-%d') }}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% set renewal_count = loan.get_renewal_count() %}
                                {% set max_renewals = loan.reader.reader_type_info.max_renewals %}
                                <span class="badge {% if renewal_count >= max_renewals %}bg-danger{% elif renewal_count > 0 %}bg-info{% else %}bg-secondary{% endif %}">
                                    {{ renewal_count }}/{{ max_renewals }}
                                </span>
                            </td>
                            <td>
                                {% if loan.return_date %}
                                <span class="status-badge badge-success">
                                    <i class="bi bi-check-circle"></i> 已归还
                                </span>
                                {% elif loan.is_overdue() %}
                                <span class="status-badge badge-danger">
                                    <i class="bi bi-exclamation-triangle"></i> 已逾期
                                </span>
                                {% else %}
                                <span class="status-badge badge-primary">
                                    <i class="bi bi-clock"></i> 借阅中
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('admin.loan_detail', loan_id=loan.loan_id) }}"
                                       class="btn btn-sm btn-outline-info"
                                       data-bs-toggle="tooltip"
                                       title="查看详情">
                                        <i class="bi bi-info-circle"></i>
                                    </a>
                                    {% if not loan.return_date %}
                                    <a href="{{ url_for('admin.return_loan', loan_id=loan.loan_id) }}"
                                       class="btn btn-sm btn-outline-success"
                                       data-bs-toggle="tooltip"
                                       title="归还图书">
                                        <i class="bi bi-journal-check"></i>
                                    </a>
                                    {% if not loan.is_overdue() %}
                                    {% set renewal_count = loan.get_renewal_count() %}
                                    {% set max_renewals = loan.reader.reader_type_info.max_renewals %}
                                    <a href="{{ url_for('admin.renew_loan', loan_id=loan.loan_id) }}"
                                       class="btn btn-sm btn-outline-primary {% if renewal_count >= max_renewals %}disabled{% endif %}"
                                       data-bs-toggle="tooltip"
                                       title="续借图书 {% if renewal_count >= max_renewals %}(已达到最大续借次数){% endif %}">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </a>
                                    {% endif %}
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <div class="p-3 d-flex justify-content-between align-items-center border-top">
                <div class="pagination-info">
                    显示 {{ pagination.items|length }} 条，共 {{ pagination.total }} 条
                </div>
                <ul class="pagination mb-0">
                    {% for page in pagination.iter_pages(left_edge=2, left_current=2, right_current=3, right_edge=2) %}
                    {% if page %}
                    <li class="page-item {% if page == pagination.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('admin.loans', page=page, q=search_query, status=status) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            {% else %}
            <div class="empty-state p-5 text-center">
                <i class="bi bi-journal-text text-muted mb-3"></i>
                <p class="mb-0">暂无借阅记录数据</p>
                <p class="text-muted small">请添加借阅记录或修改搜索条件</p>
                <a href="{{ url_for('admin.create_loan') }}" class="btn btn-primary mt-3">
                    <i class="bi bi-journal-plus"></i> 借书处理
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表格排序功能
        const sortableHeaders = document.querySelectorAll('th.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const sortBy = this.dataset.sort;
                const currentOrder = this.classList.contains('sort-asc') ? 'desc' : 'asc';

                // 清除所有排序状态
                sortableHeaders.forEach(h => {
                    h.classList.remove('sort-asc', 'sort-desc');
                });

                // 设置当前排序状态
                this.classList.add(`sort-${currentOrder}`);

                // 排序表格
                sortTable('loansTable', sortBy, currentOrder);
            });
        });

        // 初始化工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });

    // 表格排序函数
    function sortTable(tableId, sortBy, order) {
        const table = document.getElementById(tableId);
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        // 根据不同的列进行排序
        rows.sort((a, b) => {
            let aValue, bValue;

            switch(sortBy) {
                case 'id':
                    aValue = parseInt(a.cells[0].textContent.trim());
                    bValue = parseInt(b.cells[0].textContent.trim());
                    break;
                case 'reader':
                    aValue = a.cells[1].textContent.trim();
                    bValue = b.cells[1].textContent.trim();
                    break;
                case 'book':
                    aValue = a.cells[2].textContent.trim();
                    bValue = b.cells[2].textContent.trim();
                    break;
                case 'loan_date':
                    aValue = new Date(a.cells[3].textContent.trim());
                    bValue = new Date(b.cells[3].textContent.trim());
                    break;
                case 'due_date':
                    aValue = new Date(a.cells[4].textContent.trim());
                    bValue = new Date(b.cells[4].textContent.trim());
                    break;
                case 'return_date':
                    aValue = a.cells[5].textContent.trim() === '-' ? new Date(9999, 11, 31) : new Date(a.cells[5].textContent.trim());
                    bValue = b.cells[5].textContent.trim() === '-' ? new Date(9999, 11, 31) : new Date(b.cells[5].textContent.trim());
                    break;
                case 'status':
                    aValue = a.cells[6].textContent.trim();
                    bValue = b.cells[6].textContent.trim();
                    break;
                default:
                    return 0;
            }

            // 比较值
            if (aValue < bValue) {
                return order === 'asc' ? -1 : 1;
            }
            if (aValue > bValue) {
                return order === 'asc' ? 1 : -1;
            }
            return 0;
        });

        // 重新排列表格行
        rows.forEach(row => tbody.appendChild(row));
    }
</script>
{% endblock %}
