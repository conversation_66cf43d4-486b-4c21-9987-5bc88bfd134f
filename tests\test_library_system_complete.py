#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图书借阅管理系统完整自动化测试脚本
使用Playwright进行全面的功能测试和权限控制测试
"""

import asyncio
import pytest
from playwright.async_api import async_playwright, <PERSON>, <PERSON>rowser, BrowserContext
import time
import json
from datetime import datetime


class LibrarySystemTester:
    """图书借阅管理系统测试类"""
    
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.test_results = []
        self.browser = None
        self.context = None
        self.page = None
    
    async def setup(self):
        """初始化测试环境"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False, slow_mo=1000)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        
        # 设置页面超时
        self.page.set_default_timeout(10000)
        
        print("🚀 测试环境初始化完成")
    
    async def teardown(self):
        """清理测试环境"""
        if self.browser:
            await self.browser.close()
        print("🧹 测试环境清理完成")
    
    def log_test_result(self, test_name, status, message=""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "status": status,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌"
        print(f"{status_icon} {test_name}: {status} - {message}")
    
    async def navigate_to_login(self):
        """导航到登录页面"""
        try:
            await self.page.goto(self.base_url)
            await self.page.wait_for_selector('.login-card', timeout=5000)
            self.log_test_result("导航到登录页面", "PASS", "成功加载登录页面")
            return True
        except Exception as e:
            self.log_test_result("导航到登录页面", "FAIL", f"无法加载登录页面: {str(e)}")
            return False
    
    async def test_login_page_layout(self):
        """测试登录页面布局"""
        try:
            # 检查三个用户角色按钮是否在一行显示
            user_type_buttons = await self.page.query_selector_all('.btn-group .btn-outline-primary')
            
            if len(user_type_buttons) != 3:
                self.log_test_result("登录页面布局测试", "FAIL", f"用户角色按钮数量错误，期望3个，实际{len(user_type_buttons)}个")
                return False
            
            # 检查按钮文本
            expected_texts = ["读者登录", "工作人员登录", "管理员登录"]
            for i, button in enumerate(user_type_buttons):
                text = await button.inner_text()
                if expected_texts[i] not in text:
                    self.log_test_result("登录页面布局测试", "FAIL", f"按钮{i+1}文本错误，期望包含'{expected_texts[i]}'，实际'{text}'")
                    return False
            
            # 检查按钮是否在同一行（通过检查Y坐标）
            button_boxes = []
            for button in user_type_buttons:
                box = await button.bounding_box()
                button_boxes.append(box)
            
            # 检查所有按钮的Y坐标是否相近（允许5像素误差）
            first_y = button_boxes[0]['y']
            for i, box in enumerate(button_boxes[1:], 1):
                if abs(box['y'] - first_y) > 5:
                    self.log_test_result("登录页面布局测试", "FAIL", f"按钮{i+1}不在同一行，Y坐标差异: {abs(box['y'] - first_y)}")
                    return False
            
            self.log_test_result("登录页面布局测试", "PASS", "三个用户角色按钮正确显示在一行")
            return True
            
        except Exception as e:
            self.log_test_result("登录页面布局测试", "FAIL", f"测试过程中出错: {str(e)}")
            return False
    
    async def login_as_user(self, user_type, username, password):
        """登录指定用户"""
        try:
            # 选择用户类型
            await self.page.click(f'input[value="{user_type}"]')
            await self.page.wait_for_timeout(500)
            
            # 输入用户名和密码
            await self.page.fill('#username', username)
            await self.page.fill('#password', password)
            
            # 点击登录按钮
            await self.page.click('button[type="submit"]')
            
            # 等待页面跳转
            await self.page.wait_for_timeout(2000)
            
            # 检查是否登录成功（通过检查是否有侧边栏）
            sidebar = await self.page.query_selector('.sidebar')
            if sidebar:
                self.log_test_result(f"{user_type}登录测试", "PASS", f"用户 {username} 登录成功")
                return True
            else:
                # 检查是否有错误消息
                error_alert = await self.page.query_selector('.alert-danger')
                error_msg = await error_alert.inner_text() if error_alert else "未知错误"
                self.log_test_result(f"{user_type}登录测试", "FAIL", f"登录失败: {error_msg}")
                return False
                
        except Exception as e:
            self.log_test_result(f"{user_type}登录测试", "FAIL", f"登录过程中出错: {str(e)}")
            return False
    
    async def test_admin_login(self):
        """测试管理员登录"""
        await self.navigate_to_login()
        return await self.login_as_user("admin", "admin", "1")
    
    async def test_staff_login(self):
        """测试工作人员登录"""
        await self.navigate_to_login()
        return await self.login_as_user("staff", "S001", "123456")
    
    async def test_reader_login(self):
        """测试读者登录"""
        await self.navigate_to_login()
        return await self.login_as_user("reader", "2021001", "123456")
    
    async def test_admin_navigation_permissions(self):
        """测试管理员导航权限"""
        try:
            # 检查管理员应该看到的菜单项
            expected_menus = [
                "主菜单",
                "读者管理", 
                "图书管理",
                "借阅管理",
                "工作人员管理"
            ]
            
            for menu in expected_menus:
                menu_element = await self.page.query_selector(f'text="{menu}"')
                if not menu_element:
                    self.log_test_result("管理员导航权限测试", "FAIL", f"缺少菜单项: {menu}")
                    return False
            
            # 检查不应该看到的工作人员专用菜单
            staff_menus = ["工作台"]
            for menu in staff_menus:
                menu_element = await self.page.query_selector(f'.sidebar-heading:has-text("{menu}")')
                if menu_element:
                    self.log_test_result("管理员导航权限测试", "FAIL", f"错误显示工作人员菜单: {menu}")
                    return False
            
            self.log_test_result("管理员导航权限测试", "PASS", "管理员菜单权限正确")
            return True
            
        except Exception as e:
            self.log_test_result("管理员导航权限测试", "FAIL", f"测试过程中出错: {str(e)}")
            return False
    
    async def test_staff_navigation_permissions(self):
        """测试工作人员导航权限"""
        try:
            # 检查工作人员应该看到的菜单项
            expected_menus = [
                "工作台",
                "借阅管理",
                "记录查询"
            ]
            
            for menu in expected_menus:
                menu_element = await self.page.query_selector(f'.sidebar-heading:has-text("{menu}")')
                if not menu_element:
                    self.log_test_result("工作人员导航权限测试", "FAIL", f"缺少菜单项: {menu}")
                    return False
            
            # 检查不应该看到的管理员专用菜单
            admin_menus = ["主菜单", "读者管理", "图书管理", "工作人员管理"]
            for menu in admin_menus:
                menu_element = await self.page.query_selector(f'.sidebar-heading:has-text("{menu}")')
                if menu_element:
                    self.log_test_result("工作人员导航权限测试", "FAIL", f"错误显示管理员菜单: {menu}")
                    return False
            
            self.log_test_result("工作人员导航权限测试", "PASS", "工作人员菜单权限正确")
            return True
            
        except Exception as e:
            self.log_test_result("工作人员导航权限测试", "FAIL", f"测试过程中出错: {str(e)}")
            return False
    
    async def logout(self):
        """退出登录"""
        try:
            logout_link = await self.page.query_selector('a[href*="logout"]')
            if logout_link:
                await logout_link.click()
                await self.page.wait_for_timeout(1000)
                return True
            return False
        except:
            return False

    async def test_reader_navigation_permissions(self):
        """测试读者导航权限"""
        try:
            # 检查读者应该看到的菜单项
            expected_menus = [
                "主菜单",
                "图书服务"
            ]

            for menu in expected_menus:
                menu_element = await self.page.query_selector(f'.sidebar-heading:has-text("{menu}")')
                if not menu_element:
                    self.log_test_result("读者导航权限测试", "FAIL", f"缺少菜单项: {menu}")
                    return False

            # 检查不应该看到的管理员和工作人员专用菜单
            restricted_menus = ["工作台", "读者管理", "图书管理", "工作人员管理"]
            for menu in restricted_menus:
                menu_element = await self.page.query_selector(f'.sidebar-heading:has-text("{menu}")')
                if menu_element:
                    self.log_test_result("读者导航权限测试", "FAIL", f"错误显示受限菜单: {menu}")
                    return False

            self.log_test_result("读者导航权限测试", "PASS", "读者菜单权限正确")
            return True

        except Exception as e:
            self.log_test_result("读者导航权限测试", "FAIL", f"测试过程中出错: {str(e)}")
            return False

    async def test_fine_calculation(self):
        """测试罚款计算逻辑"""
        try:
            # 这里需要模拟一个逾期的借阅记录来测试罚款计算
            # 由于这需要数据库操作，我们先检查页面上是否有罚款显示

            # 导航到借阅记录页面
            loans_link = await self.page.query_selector('a[href*="loans"]')
            if loans_link:
                await loans_link.click()
                await self.page.wait_for_timeout(2000)

                # 查找是否有罚款信息显示
                fine_elements = await self.page.query_selector_all('text=/罚款|逾期/')

                if fine_elements:
                    # 检查罚款计算是否按0.5元/天标准
                    for element in fine_elements:
                        text = await element.inner_text()
                        # 这里可以添加更具体的罚款计算验证逻辑

                    self.log_test_result("罚款计算测试", "PASS", "找到罚款相关信息")
                else:
                    self.log_test_result("罚款计算测试", "PASS", "当前无逾期记录")

                return True
            else:
                self.log_test_result("罚款计算测试", "FAIL", "无法找到借阅记录链接")
                return False

        except Exception as e:
            self.log_test_result("罚款计算测试", "FAIL", f"测试过程中出错: {str(e)}")
            return False

    async def test_staff_functions(self):
        """测试工作人员功能"""
        try:
            # 测试办理借书功能
            borrow_link = await self.page.query_selector('a[href*="borrow"]')
            if borrow_link:
                await borrow_link.click()
                await self.page.wait_for_timeout(2000)

                # 检查是否有借书表单
                form = await self.page.query_selector('form')
                if form:
                    self.log_test_result("工作人员借书功能测试", "PASS", "成功访问借书页面")
                else:
                    self.log_test_result("工作人员借书功能测试", "FAIL", "借书页面缺少表单")
                    return False

            # 测试办理还书功能
            return_link = await self.page.query_selector('a[href*="return"]')
            if return_link:
                await return_link.click()
                await self.page.wait_for_timeout(2000)

                # 检查是否有还书表单
                form = await self.page.query_selector('form')
                if form:
                    self.log_test_result("工作人员还书功能测试", "PASS", "成功访问还书页面")
                else:
                    self.log_test_result("工作人员还书功能测试", "FAIL", "还书页面缺少表单")
                    return False

            # 测试罚款管理功能
            fine_link = await self.page.query_selector('a[href*="fine"]')
            if fine_link:
                await fine_link.click()
                await self.page.wait_for_timeout(2000)

                # 检查是否有罚款管理页面
                page_content = await self.page.content()
                if "罚款" in page_content:
                    self.log_test_result("工作人员罚款管理测试", "PASS", "成功访问罚款管理页面")
                else:
                    self.log_test_result("工作人员罚款管理测试", "FAIL", "罚款管理页面内容异常")
                    return False

            return True

        except Exception as e:
            self.log_test_result("工作人员功能测试", "FAIL", f"测试过程中出错: {str(e)}")
            return False

    async def test_admin_functions(self):
        """测试管理员功能"""
        try:
            # 测试读者管理功能
            readers_link = await self.page.query_selector('a[href*="readers"]')
            if readers_link:
                await readers_link.click()
                await self.page.wait_for_timeout(2000)

                # 检查是否有读者列表
                table = await self.page.query_selector('table')
                if table:
                    self.log_test_result("管理员读者管理测试", "PASS", "成功访问读者管理页面")
                else:
                    self.log_test_result("管理员读者管理测试", "FAIL", "读者管理页面缺少表格")
                    return False

            # 测试图书管理功能
            books_link = await self.page.query_selector('a[href*="books"]')
            if books_link:
                await books_link.click()
                await self.page.wait_for_timeout(2000)

                # 检查是否有图书列表
                table = await self.page.query_selector('table')
                if table:
                    self.log_test_result("管理员图书管理测试", "PASS", "成功访问图书管理页面")
                else:
                    self.log_test_result("管理员图书管理测试", "FAIL", "图书管理页面缺少表格")
                    return False

            # 测试工作人员管理功能
            staff_link = await self.page.query_selector('a[href*="staff"]')
            if staff_link:
                await staff_link.click()
                await self.page.wait_for_timeout(2000)

                # 检查是否有工作人员管理页面
                page_content = await self.page.content()
                if "工作人员" in page_content:
                    self.log_test_result("管理员工作人员管理测试", "PASS", "成功访问工作人员管理页面")
                else:
                    self.log_test_result("管理员工作人员管理测试", "FAIL", "工作人员管理页面内容异常")
                    return False

            return True

        except Exception as e:
            self.log_test_result("管理员功能测试", "FAIL", f"测试过程中出错: {str(e)}")
            return False

    async def test_reader_functions(self):
        """测试读者功能"""
        try:
            # 测试图书检索功能
            books_link = await self.page.query_selector('a[href*="books"]')
            if books_link:
                await books_link.click()
                await self.page.wait_for_timeout(2000)

                # 检查是否有搜索功能
                search_input = await self.page.query_selector('input[type="search"], input[placeholder*="搜索"], input[placeholder*="检索"]')
                if search_input:
                    self.log_test_result("读者图书检索测试", "PASS", "成功访问图书检索页面")
                else:
                    self.log_test_result("读者图书检索测试", "FAIL", "图书检索页面缺少搜索功能")
                    return False

            # 测试借阅记录查看功能
            loans_link = await self.page.query_selector('a[href*="loans"]')
            if loans_link:
                await loans_link.click()
                await self.page.wait_for_timeout(2000)

                # 检查是否有借阅记录
                page_content = await self.page.content()
                if "借阅记录" in page_content or "暂无记录" in page_content:
                    self.log_test_result("读者借阅记录测试", "PASS", "成功访问借阅记录页面")
                else:
                    self.log_test_result("读者借阅记录测试", "FAIL", "借阅记录页面内容异常")
                    return False

            return True

        except Exception as e:
            self.log_test_result("读者功能测试", "FAIL", f"测试过程中出错: {str(e)}")
            return False

    async def run_all_tests(self):
        """运行所有测试"""
        print("🎯 开始运行图书借阅管理系统完整测试套件")
        print("=" * 60)

        # 初始化测试环境
        await self.setup()

        try:
            # 1. 登录页面布局测试
            await self.navigate_to_login()
            await self.test_login_page_layout()

            # 2. 管理员登录和权限测试
            if await self.test_admin_login():
                await self.test_admin_navigation_permissions()
                await self.test_admin_functions()
                await self.logout()

            # 3. 工作人员登录和权限测试
            if await self.test_staff_login():
                await self.test_staff_navigation_permissions()
                await self.test_staff_functions()
                await self.test_fine_calculation()
                await self.logout()

            # 4. 读者登录和权限测试
            if await self.test_reader_login():
                await self.test_reader_navigation_permissions()
                await self.test_reader_functions()
                await self.logout()

        except Exception as e:
            self.log_test_result("测试套件执行", "FAIL", f"测试过程中发生严重错误: {str(e)}")

        finally:
            # 清理测试环境
            await self.teardown()

            # 生成测试报告
            self.generate_test_report()

    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "PASS"])
        failed_tests = total_tests - passed_tests

        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "成功率: 0%")

        print("\n📋 详细测试结果:")
        print("-" * 60)

        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {result['test_name']}: {result['status']}")
            if result["message"]:
                print(f"   💬 {result['message']}")

        # 保存测试报告到文件
        report_data = {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests/total_tests*100) if total_tests > 0 else 0
            },
            "test_results": self.test_results,
            "generated_at": datetime.now().isoformat()
        }

        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        print(f"\n📄 详细测试报告已保存到: test_report.json")

        if failed_tests > 0:
            print("\n⚠️  发现测试失败，请检查以下问题:")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    print(f"   • {result['test_name']}: {result['message']}")


async def main():
    """主函数"""
    tester = LibrarySystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
