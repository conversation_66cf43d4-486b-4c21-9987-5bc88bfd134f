{% extends 'base.html' %}

{% block title %}添加图书 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 添加图书卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">添加图书</h6>
                    <a href="{{ url_for('admin.books') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('admin.create_book') }}" method="post">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="isbn" class="form-label">ISBN <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="isbn" name="isbn" required>
                                <div class="form-text">图书的国际标准书号，作为唯一标识</div>
                            </div>
                            <div class="col-md-6">
                                <label for="title" class="form-label">书名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="author" class="form-label">作者 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="author" name="author" required>
                            </div>
                            <div class="col-md-6">
                                <label for="publisher" class="form-label">出版社 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="publisher" name="publisher" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="year" class="form-label">出版年份 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="year" name="year" min="1000" max="9999" required>
                            </div>
                            <div class="col-md-8">
                                <label for="book_category_id" class="form-label">图书类别 <span class="text-danger">*</span></label>
                                <select class="form-select" id="book_category_id" name="book_category_id" required>
                                    <option value="">请选择类别</option>
                                    {% for category in categories %}
                                    <option value="{{ category.book_category_id }}">
                                        {{ category.category_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="quantity" class="form-label">总数量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="quantity" name="quantity" min="0" required>
                            </div>
                            <div class="col-md-6">
                                <label for="available_quantity" class="form-label">可借数量</label>
                                <input type="number" class="form-control" id="available_quantity" name="available_quantity" min="0">
                                <div class="form-text">如果不填写，默认等于总数量</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 设置可借数量默认等于总数量
        const quantityInput = document.getElementById('quantity');
        const availableQuantityInput = document.getElementById('available_quantity');
        
        quantityInput.addEventListener('input', function() {
            if (!availableQuantityInput.value) {
                availableQuantityInput.value = this.value;
            }
        });
        
        // 表单验证
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            // 验证ISBN格式
            const isbnInput = document.getElementById('isbn');
            const isbnValue = isbnInput.value.trim();
            if (!/^[0-9-]{10,20}$/.test(isbnValue)) {
                alert('ISBN格式不正确，应为10-20位数字和连字符');
                isbnInput.focus();
                event.preventDefault();
                return;
            }
            
            // 验证可借数量不超过总数量
            const quantityValue = parseInt(quantityInput.value);
            const availableQuantityValue = parseInt(availableQuantityInput.value);
            if (availableQuantityValue > quantityValue) {
                alert('可借数量不能超过总数量');
                availableQuantityInput.focus();
                event.preventDefault();
                return;
            }
        });
    });
</script>
{% endblock %}
