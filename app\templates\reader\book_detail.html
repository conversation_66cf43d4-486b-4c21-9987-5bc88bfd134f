{% extends 'base.html' %}

{% block title %}图书详情 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/reader.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8">
            <!-- 图书详情卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">图书详情</h6>
                    <a href="{{ url_for('reader.books') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 20%">ISBN</th>
                                    <td>{{ book.isbn }}</td>
                                </tr>
                                <tr>
                                    <th>书名</th>
                                    <td>{{ book.title }}</td>
                                </tr>
                                <tr>
                                    <th>作者</th>
                                    <td>{{ book.author }}</td>
                                </tr>
                                <tr>
                                    <th>出版社</th>
                                    <td>{{ book.publisher }}</td>
                                </tr>
                                <tr>
                                    <th>出版年份</th>
                                    <td>{{ book.year }}</td>
                                </tr>
                                <tr>
                                    <th>图书类别</th>
                                    <td>{{ book.category.category_name }}</td>
                                </tr>
                                <tr>
                                    <th>总数量</th>
                                    <td>{{ book.quantity }}</td>
                                </tr>
                                <tr>
                                    <th>可借数量</th>
                                    <td>
                                        {% if book.available_quantity > 0 %}
                                        <span class="badge bg-success">{{ book.available_quantity }}</span>
                                        {% else %}
                                        <span class="badge bg-danger">0</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 图书简介 -->
                    <div class="mt-4">
                        <h5 class="font-weight-bold">图书简介</h5>
                        <p class="text-muted">
                            这是《{{ book.title }}》的简介。由于数据库中没有存储图书简介，这里显示的是默认文本。
                            在实际应用中，应该从数据库中读取图书的详细介绍，并在此处显示。
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 借阅状态卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">借阅状态</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        {% if current_loan %}
                        <div class="mb-3">
                            <span class="badge bg-info p-2">已借阅</span>
                        </div>
                        <p class="text-info">您已借阅此图书，应还日期：{{ current_loan.due_date|datetime('%Y-%m-%d') }}</p>
                        {% elif book.available_quantity > 0 %}
                        <div class="mb-3">
                            <span class="badge bg-success p-2">可借阅</span>
                        </div>
                        <p class="text-success">当前有 {{ book.available_quantity }} 本可借</p>
                        {% else %}
                        <div class="mb-3">
                            <span class="badge bg-danger p-2">不可借阅</span>
                        </div>
                        <p class="text-danger">当前无可借图书</p>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        {% if current_loan %}
                        <!-- 已借阅此图书，显示归还按钮 -->
                        <a href="{{ url_for('reader.return_book', loan_id=current_loan.loan_id) }}" class="btn btn-warning">
                            <i class="bi bi-journal-arrow-up"></i> 归还图书
                        </a>
                        {% elif book.available_quantity > 0 %}
                        <!-- 未借阅且有可借数量，显示借阅按钮 -->
                        <form action="{{ url_for('reader.borrow_book', isbn=book.isbn) }}" method="post">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-journal-plus"></i> 借阅此书
                            </button>
                        </form>
                        {% else %}
                        <!-- 未借阅且无可借数量，显示禁用按钮 -->
                        <button class="btn btn-secondary" disabled>
                            <i class="bi bi-journal-x"></i> 暂不可借
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 借阅信息卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">借阅信息</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            借阅期限
                            <span class="badge bg-primary rounded-pill">{{ current_user.reader_type_info.borrow_duration }} 天</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            最大续借次数
                            <span class="badge bg-primary rounded-pill">{{ current_user.reader_type_info.max_renewals }} 次</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            逾期罚款
                            <span class="badge bg-warning text-dark rounded-pill">{{ book.category.fine_amount }} 元/天</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
