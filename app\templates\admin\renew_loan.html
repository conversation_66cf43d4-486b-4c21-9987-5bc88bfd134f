{% extends 'base.html' %}

{% block title %}续借图书 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 续借图书卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">续借图书</h6>
                    <div>
                        <a href="{{ url_for('admin.loan_detail', loan_id=loan.loan_id) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回详情
                        </a>
                        <a href="{{ url_for('admin.loans') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-list"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="bi bi-info-circle me-2"></i>
                        您正在处理图书续借，请确认以下信息无误。
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">读者信息</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1"><strong>读者ID:</strong> {{ loan.reader_id }}</p>
                                    <p class="mb-1"><strong>姓名:</strong> {{ loan.reader.name }}</p>
                                    <p class="mb-0"><strong>类型:</strong> {{ loan.reader.reader_type }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">图书信息</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1"><strong>ISBN:</strong> {{ loan.isbn }}</p>
                                    <p class="mb-1"><strong>书名:</strong> {{ loan.book.title }}</p>
                                    <p class="mb-0"><strong>作者:</strong> {{ loan.book.author }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">当前借阅信息</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1"><strong>借阅日期:</strong> {{ loan.loan_date|datetime('%Y-%m-%d') }}</p>
                                    <p class="mb-1"><strong>当前应还日期:</strong> {{ loan.due_date|datetime('%Y-%m-%d') }}</p>
                                    <p class="mb-0"><strong>剩余天数:</strong> {{ remaining_days }} 天</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">续借信息</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1"><strong>续借期限:</strong> {{ loan.reader.reader_type_info.borrow_duration }} 天</p>
                                    <p class="mb-1"><strong>续借后应还日期:</strong> {{ new_due_date|datetime('%Y-%m-%d') }}</p>
                                    <p class="mb-1"><strong>已续借次数:</strong> {{ loan.get_renewal_count() }} 次</p>
                                    <p class="mb-0"><strong>最大续借次数:</strong> {{ loan.reader.reader_type_info.max_renewals }} 次</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form action="{{ url_for('admin.renew_loan', loan_id=loan.loan_id) }}" method="post">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('admin.loan_detail', loan_id=loan.loan_id) }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-arrow-repeat"></i> 确认续借
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
