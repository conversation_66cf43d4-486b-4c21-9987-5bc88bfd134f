#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图书相关模型
"""

from .. import db


class BookCategory(db.Model):
    """图书类别模型"""
    __tablename__ = 'BookCategory'

    book_category_id = db.Column('BookCategoryID', db.String(10), primary_key=True)
    category_name = db.Column('CategoryName', db.String(50), nullable=False)
    fine_amount = db.Column('FineAmount', db.Float, default=0.1)

    # 关系
    books = db.relationship('Book', backref='category', lazy=True)

    def __repr__(self):
        return f'<BookCategory {self.category_name}>'


class Book(db.Model):
    """图书模型"""
    __tablename__ = 'Books'

    isbn = db.Column('ISBN', db.String(20), primary_key=True)
    title = db.Column('Title', db.String(255), nullable=False)
    author = db.Column('Author', db.String(100), nullable=False)
    publisher = db.Column('Publisher', db.String(100), nullable=False)
    year = db.Column('Year', db.Integer, nullable=False)
    book_category_id = db.Column('BookCategoryID', db.String(10), db.ForeignKey('BookCategory.BookCategoryID'), nullable=False)
    quantity = db.Column('Quantity', db.Integer, nullable=False)
    available_quantity = db.Column('AvailableQuantity', db.Integer)

    # 关系
    loans = db.relationship('Loan', backref='book', lazy=True)

    def __repr__(self):
        return f'<Book {self.title}>'

    def is_available(self):
        """检查图书是否可借"""
        return self.available_quantity > 0

    def get_current_loans(self):
        """获取当前借阅"""
        return [loan for loan in self.loans if loan.return_date is None]

    def get_loan_history(self):
        """获取借阅历史"""
        return [loan for loan in self.loans if loan.return_date is not None]
