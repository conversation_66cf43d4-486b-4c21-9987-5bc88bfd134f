/* 
 * 读者界面样式文件
 * 统一读者界面的UI风格，与管理员界面保持一致
 */

/* 读者仪表盘样式 */
.stat-card {
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background-color: white;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card-primary {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
    color: white;
}

.stat-card-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    font-size: 2.5rem;
    opacity: 0.2;
}

.stat-card-title {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    margin-bottom: 0.75rem;
}

.stat-card-value {
    font-size: 2.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card-desc {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* 图书卡片样式 */
.book-card {
    transition: all 0.3s ease;
    border-radius: 0.75rem;
    overflow: hidden;
    height: 100%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.book-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.book-card-img {
    height: 200px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.book-card-body {
    padding: 1.5rem;
}

.book-card-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.book-card-author {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.book-card-footer {
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* 搜索表单样式 */
.search-form .input-group-text {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    color: #4a5568;
}

.search-form .form-control {
    border-color: #e2e8f0;
    box-shadow: none;
    font-size: 1rem;
}

.search-form .form-control:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
}

.search-form .form-select {
    border-color: #e2e8f0;
    box-shadow: none;
    font-size: 1rem;
}

.search-form .form-select:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
}

/* 借阅记录页面样式 */
.loan-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 50rem;
    font-weight: 500;
    font-size: 0.9rem;
}

.loan-status i {
    margin-right: 0.5rem;
}

.loan-status-normal {
    background-color: rgba(72, 187, 120, 0.1);
    color: #38a169;
}

.loan-status-warning {
    background-color: rgba(237, 137, 54, 0.1);
    color: #ed8936;
}

.loan-status-overdue {
    background-color: rgba(245, 101, 101, 0.1);
    color: #f56565;
}

/* 图书详情页面样式 */
.book-detail-cover {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
    border-radius: 0.75rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.book-detail-info {
    margin-bottom: 2rem;
}

.book-detail-info h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.book-detail-info p {
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.book-detail-info .badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.book-detail-description {
    margin-top: 2rem;
}

.book-detail-description h5 {
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.book-detail-description p {
    line-height: 1.6;
    color: #4a5568;
}

/* 预约页面样式 */
.reservation-card {
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 1.5rem;
    background-color: white;
    transition: all 0.3s ease;
}

.reservation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.reservation-card .card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

.reservation-card .card-body {
    padding: 1.5rem;
}

.reservation-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 50rem;
    font-weight: 500;
    font-size: 0.9rem;
}

.reservation-status i {
    margin-right: 0.5rem;
}

.reservation-status-pending {
    background-color: rgba(237, 137, 54, 0.1);
    color: #ed8936;
}

.reservation-status-approved {
    background-color: rgba(72, 187, 120, 0.1);
    color: #38a169;
}

.reservation-status-rejected {
    background-color: rgba(245, 101, 101, 0.1);
    color: #f56565;
}

.reservation-status-expired {
    background-color: rgba(160, 174, 192, 0.1);
    color: #718096;
}

/* 个人信息页面样式 */
.profile-card {
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 1.5rem;
    background-color: white;
}

.profile-header {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
    color: white;
    padding: 2rem;
    text-align: center;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: white;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    margin: 0 auto 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.profile-name {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.profile-info {
    opacity: 0.9;
    font-size: 1.1rem;
}

.profile-body {
    padding: 2rem;
}

.profile-section {
    margin-bottom: 2rem;
}

.profile-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

.profile-section-title i {
    margin-right: 0.75rem;
    color: var(--primary-light);
}

.profile-item {
    margin-bottom: 1rem;
    display: flex;
}

.profile-item-label {
    font-weight: 600;
    width: 150px;
    color: var(--dark-color);
}

.profile-item-value {
    flex: 1;
    color: var(--secondary-color);
}
