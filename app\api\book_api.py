#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图书API资源
"""

from flask import request, current_app
from flask_restful import Resource
from marshmallow import ValidationError
from sqlalchemy import or_

from .. import db
from ..models.book import Book, BookCategory
from ..schemas.book_schema import BookSchema, BookCategorySchema, BookSearchSchema
from ..utils.db import get_or_404


class BookListResource(Resource):
    """图书列表资源"""
    
    def get(self):
        """获取所有图书"""
        books = Book.query.all()
        return BookSchema(many=True).dump(books)
    
    def post(self):
        """创建图书"""
        json_data = request.get_json()
        if not json_data:
            return {'message': '没有提供数据'}, 400
        
        try:
            book_data = BookSchema().load(json_data)
        except ValidationError as err:
            return {'message': '数据验证错误', 'errors': err.messages}, 422
        
        # 检查ISBN是否已存在
        if Book.query.get(book_data.isbn):
            return {'message': f'ISBN {book_data.isbn} 已存在'}, 409
        
        # 保存图书
        db.session.add(book_data)
        db.session.commit()
        
        return BookSchema().dump(book_data), 201


class BookResource(Resource):
    """图书资源"""
    
    def get(self, isbn):
        """获取图书"""
        book = get_or_404(Book, isbn, f'图书 {isbn} 不存在')
        return BookSchema().dump(book)
    
    def put(self, isbn):
        """更新图书"""
        book = get_or_404(Book, isbn, f'图书 {isbn} 不存在')
        
        json_data = request.get_json()
        if not json_data:
            return {'message': '没有提供数据'}, 400
        
        try:
            book_data = BookSchema().load(json_data, instance=book, partial=True)
        except ValidationError as err:
            return {'message': '数据验证错误', 'errors': err.messages}, 422
        
        db.session.commit()
        
        return BookSchema().dump(book_data)
    
    def delete(self, isbn):
        """删除图书"""
        book = get_or_404(Book, isbn, f'图书 {isbn} 不存在')
        
        db.session.delete(book)
        db.session.commit()
        
        return {'message': f'图书 {isbn} 已删除'}


class BookSearchResource(Resource):
    """图书搜索资源"""
    
    def get(self):
        """搜索图书"""
        args = request.args.to_dict()
        
        try:
            search_data = BookSearchSchema().load(args)
        except ValidationError as err:
            return {'message': '数据验证错误', 'errors': err.messages}, 422
        
        # 构建查询
        query = Book.query
        
        # 添加搜索条件
        if 'isbn' in search_data:
            query = query.filter(Book.isbn.like(f'%{search_data["isbn"]}%'))
        
        if 'title' in search_data:
            query = query.filter(Book.title.like(f'%{search_data["title"]}%'))
        
        if 'author' in search_data:
            query = query.filter(Book.author.like(f'%{search_data["author"]}%'))
        
        if 'publisher' in search_data:
            query = query.filter(Book.publisher.like(f'%{search_data["publisher"]}%'))
        
        if 'year' in search_data:
            query = query.filter(Book.year == search_data['year'])
        
        if 'book_category_id' in search_data:
            query = query.filter(Book.book_category_id == search_data['book_category_id'])
        
        # 分页
        page = search_data.get('page', 1)
        per_page = search_data.get('per_page', 10)
        
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        books = pagination.items
        
        return {
            'items': BookSchema(many=True).dump(books),
            'total': pagination.total,
            'pages': pagination.pages,
            'page': page,
            'per_page': per_page
        }
