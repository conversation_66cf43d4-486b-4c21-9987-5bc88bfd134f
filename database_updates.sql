-- 图书借阅管理系统数据库扩展脚本
-- 用于支持三类用户：系统管理员、读者、工作人员

USE LibraryDB;

-- =====================================================
-- 步骤1.1：创建工作人员表 (Staff)
-- =====================================================

-- 创建工作人员表
CREATE TABLE IF NOT EXISTS Staff (
    StaffID VARCHAR(10) NOT NULL PRIMARY KEY,           -- 工作证号
    Name VARCHAR(50) NOT NULL,                          -- 姓名
    Phone VARCHAR(15),                                  -- 电话
    Department VARCHAR(100),                            -- 部门
    Position VARCHAR(50),                               -- 职位
    HireDate DATE DEFAULT (CURRENT_DATE),               -- 入职日期（默认当前日期）
    Status ENUM('active', 'inactive') DEFAULT 'active', -- 状态（活跃/非活跃）
    Pwd VARCHAR(20) NOT NULL DEFAULT '123456',          -- 密码（默认123456）
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- 更新时间
);

-- 添加工作人员表注释
ALTER TABLE Staff COMMENT = '工作人员信息表';
ALTER TABLE Staff MODIFY COLUMN StaffID VARCHAR(10) COMMENT '工作证号（主键）';
ALTER TABLE Staff MODIFY COLUMN Name VARCHAR(50) COMMENT '工作人员姓名';
ALTER TABLE Staff MODIFY COLUMN Phone VARCHAR(15) COMMENT '联系电话';
ALTER TABLE Staff MODIFY COLUMN Department VARCHAR(100) COMMENT '所属部门';
ALTER TABLE Staff MODIFY COLUMN Position VARCHAR(50) COMMENT '职位';
ALTER TABLE Staff MODIFY COLUMN HireDate DATE COMMENT '入职日期';
ALTER TABLE Staff MODIFY COLUMN Status ENUM('active', 'inactive') COMMENT '状态：active-活跃，inactive-非活跃';
ALTER TABLE Staff MODIFY COLUMN Pwd VARCHAR(20) COMMENT '登录密码';

-- =====================================================
-- 步骤1.2：扩展读者表，添加证件状态字段
-- =====================================================

-- 为读者表添加证件状态字段
ALTER TABLE Readers ADD COLUMN IF NOT EXISTS Status ENUM('valid', 'invalid') DEFAULT 'valid' COMMENT '证件状态：valid-有效，invalid-失效';

-- 为读者表添加更新时间字段（如果不存在）
ALTER TABLE Readers ADD COLUMN IF NOT EXISTS UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间';

-- =====================================================
-- 步骤1.3：完善借阅表，优化罚款计算
-- =====================================================

-- 确保借阅表有正确的罚款字段类型
ALTER TABLE Loans MODIFY COLUMN FineAmount DECIMAL(10, 2) DEFAULT 0.00 COMMENT '罚款金额';

-- 添加罚款状态字段
ALTER TABLE Loans ADD COLUMN IF NOT EXISTS FineStatus ENUM('unpaid', 'paid', 'waived') DEFAULT 'unpaid' COMMENT '罚款状态：unpaid-未付，paid-已付，waived-免除';

-- 添加处理工作人员字段（记录是谁处理的借阅/归还）
ALTER TABLE Loans ADD COLUMN IF NOT EXISTS ProcessedBy VARCHAR(10) COMMENT '处理人员（工作人员ID或admin）';

-- 添加处理时间字段
ALTER TABLE Loans ADD COLUMN IF NOT EXISTS ProcessedAt TIMESTAMP NULL COMMENT '处理时间';

-- 添加备注字段
ALTER TABLE Loans ADD COLUMN IF NOT EXISTS Remarks TEXT COMMENT '备注信息';

-- =====================================================
-- 插入测试数据
-- =====================================================

-- 插入测试工作人员数据
INSERT IGNORE INTO Staff (StaffID, Name, Phone, Department, Position, HireDate, Status, Pwd) VALUES
('S001', '张图书', '13800138001', '借阅部', '图书管理员', '2023-01-15', 'active', '123456'),
('S002', '李管理', '13800138002', '借阅部', '高级管理员', '2022-06-01', 'active', '123456'),
('S003', '王助理', '13800138003', '技术部', '系统助理', '2023-03-20', 'active', '123456'),
('S004', '赵老师', '13800138004', '借阅部', '图书管理员', '2021-09-01', 'inactive', '123456');

-- 更新现有读者的证件状态（设置一些为失效状态用于测试）
UPDATE Readers SET Status = 'valid' WHERE ReaderID IN ('2021001', '2021002', '2021003');
UPDATE Readers SET Status = 'invalid' WHERE ReaderID = '2021004';

-- =====================================================
-- 创建视图和索引优化
-- =====================================================

-- 创建活跃工作人员视图
CREATE OR REPLACE VIEW ActiveStaff AS
SELECT 
    StaffID,
    Name,
    Phone,
    Department,
    Position,
    HireDate,
    DATEDIFF(CURRENT_DATE, HireDate) AS WorkDays
FROM Staff 
WHERE Status = 'active'
ORDER BY HireDate;

-- 创建有效读者视图
CREATE OR REPLACE VIEW ValidReaders AS
SELECT 
    r.ReaderID,
    r.Name,
    r.Department,
    r.Phone,
    r.Email,
    r.ReaderType,
    r.RegistrationDate,
    rt.MaxBooks,
    rt.BorrowDuration
FROM Readers r
JOIN ReadersType rt ON r.ReaderType = rt.ReaderType
WHERE r.Status = 'valid'
ORDER BY r.RegistrationDate DESC;

-- 创建逾期借阅详情视图
CREATE OR REPLACE VIEW OverdueLoansDetail AS
SELECT 
    l.LoanID,
    l.ReaderID,
    r.Name AS ReaderName,
    l.ISBN,
    b.Title AS BookTitle,
    l.LoanDate,
    l.DueDate,
    DATEDIFF(CURRENT_DATE, l.DueDate) AS OverdueDays,
    CASE 
        WHEN DATEDIFF(CURRENT_DATE, l.DueDate) > 0 
        THEN DATEDIFF(CURRENT_DATE, l.DueDate) * 0.5 
        ELSE 0 
    END AS CalculatedFine,
    l.FineAmount,
    l.FineStatus,
    l.ProcessedBy
FROM Loans l
JOIN Readers r ON l.ReaderID = r.ReaderID
JOIN Books b ON l.ISBN = b.ISBN
WHERE l.ReturnDate IS NULL 
  AND l.DueDate < CURRENT_DATE
ORDER BY l.DueDate;

-- =====================================================
-- 创建索引优化查询性能
-- =====================================================

-- 为工作人员表创建索引
CREATE INDEX IF NOT EXISTS idx_staff_status ON Staff(Status);
CREATE INDEX IF NOT EXISTS idx_staff_department ON Staff(Department);

-- 为读者表状态字段创建索引
CREATE INDEX IF NOT EXISTS idx_readers_status ON Readers(Status);

-- 为借阅表罚款相关字段创建索引
CREATE INDEX IF NOT EXISTS idx_loans_fine_status ON Loans(FineStatus);
CREATE INDEX IF NOT EXISTS idx_loans_processed_by ON Loans(ProcessedBy);
CREATE INDEX IF NOT EXISTS idx_loans_due_date ON Loans(DueDate);

-- =====================================================
-- 创建触发器：自动计算罚款
-- =====================================================

DELIMITER //

-- 创建触发器：在归还图书时自动计算罚款
CREATE TRIGGER IF NOT EXISTS trg_CalculateFineOnReturn
BEFORE UPDATE ON Loans
FOR EACH ROW
BEGIN
    -- 只有在设置归还日期时才计算罚款
    IF NEW.ReturnDate IS NOT NULL AND OLD.ReturnDate IS NULL THEN
        -- 计算逾期天数
        SET @overdue_days = DATEDIFF(NEW.ReturnDate, OLD.DueDate);
        
        -- 如果逾期，计算罚款（每天0.5元）
        IF @overdue_days > 0 THEN
            SET NEW.FineAmount = @overdue_days * 0.5;
            SET NEW.FineStatus = 'unpaid';
        ELSE
            SET NEW.FineAmount = 0.00;
            SET NEW.FineStatus = 'paid';
        END IF;
        
        -- 设置处理时间
        SET NEW.ProcessedAt = CURRENT_TIMESTAMP;
    END IF;
END//

DELIMITER ;

-- =====================================================
-- 数据完整性检查
-- =====================================================

-- 检查工作人员表是否创建成功
SELECT 'Staff table created successfully' AS Status, COUNT(*) AS RecordCount FROM Staff;

-- 检查读者表状态字段是否添加成功
SELECT 'Reader status field added' AS Status, 
       COUNT(*) AS TotalReaders,
       SUM(CASE WHEN Status = 'valid' THEN 1 ELSE 0 END) AS ValidReaders,
       SUM(CASE WHEN Status = 'invalid' THEN 1 ELSE 0 END) AS InvalidReaders
FROM Readers;

-- 检查借阅表字段是否添加成功
SELECT 'Loan table enhanced' AS Status, COUNT(*) AS TotalLoans FROM Loans;

-- 显示所有视图
SHOW TABLES LIKE '%Staff%';
SHOW TABLES LIKE '%Valid%';
SHOW TABLES LIKE '%Overdue%';

COMMIT;

-- 脚本执行完成提示
SELECT '数据库扩展脚本执行完成！' AS Message, 
       '工作人员表已创建，读者表已扩展，借阅表已完善' AS Details,
       NOW() AS CompletedAt;
