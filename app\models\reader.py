#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
读者相关模型
"""

from datetime import datetime
import pytz
from flask_login import UserMixin

from .. import db, login_manager


class ReaderType(db.Model):
    """读者类型模型"""
    __tablename__ = 'ReadersType'

    reader_type = db.Column('ReaderType', db.String(20), primary_key=True)
    max_books = db.Column('MaxBooks', db.Integer)
    borrow_duration = db.Column('BorrowDuration', db.Integer)
    max_renewals = db.Column('MaxRenewals', db.Integer)

    # 关系
    readers = db.relationship('Reader', backref='reader_type_info', lazy=True)

    def __repr__(self):
        return f'<ReaderType {self.reader_type}>'


class Reader(db.Model, UserMixin):
    """读者模型"""
    __tablename__ = 'Readers'

    reader_id = db.Column('ReaderID', db.String(10), primary_key=True)
    name = db.Column('Name', db.String(50))
    department = db.Column('Department', db.String(100))
    phone = db.Column('Phone', db.String(15))
    email = db.Column('Email', db.String(100))
    reader_type = db.Column('ReaderType', db.String(20), db.ForeignKey('ReadersType.ReaderType'))
    registration_date = db.Column('RegistrationDate', db.Date, default=datetime.now(pytz.timezone('Asia/Shanghai')).date)
    pwd = db.Column('Pwd', db.String(20), default='123456')
    status = db.Column('Status', db.Enum('valid', 'invalid'), default='valid')

    # 关系
    loans = db.relationship('Loan', backref='reader', lazy=True)

    def __repr__(self):
        return f'<Reader {self.reader_id}>'

    def get_id(self):
        """获取用户ID"""
        return self.reader_id

    def verify_password(self, password):
        """验证密码"""
        # 根据特殊要求，使用明文密码存储
        return self.pwd == password

    def can_borrow(self):
        """检查是否可以借书"""
        # 获取当前借阅数量
        current_loans = sum(1 for loan in self.loans if loan.return_date is None)
        # 获取最大可借数量
        max_books = self.reader_type_info.max_books
        return current_loans < max_books

    def get_current_loans(self):
        """获取当前借阅"""
        return [loan for loan in self.loans if loan.return_date is None]

    def get_loan_history(self):
        """获取借阅历史"""
        return [loan for loan in self.loans if loan.return_date is not None]

    def get_overdue_loans(self):
        """获取逾期借阅"""
        now = datetime.now(pytz.timezone('Asia/Shanghai'))
        overdue_loans = []
        for loan in self.get_current_loans():
            # 确保时区一致
            if not loan.due_date.tzinfo:
                due_date = pytz.timezone('Asia/Shanghai').localize(loan.due_date)
                if due_date < now:
                    overdue_loans.append(loan)
            else:
                if loan.due_date < now:
                    overdue_loans.append(loan)
        return overdue_loans

    def is_valid(self):
        """检查证件是否有效"""
        return self.status == 'valid'

    def set_status(self, status):
        """设置证件状态"""
        if status in ['valid', 'invalid']:
            self.status = status
            db.session.commit()
            return True
        return False

    def activate(self):
        """激活证件"""
        return self.set_status('valid')

    def deactivate(self):
        """停用证件"""
        return self.set_status('invalid')

    def can_borrow(self):
        """检查是否可以借阅图书"""
        # 证件必须有效
        if not self.is_valid():
            return False, "证件已失效，无法借阅图书"

        # 检查当前借阅数量
        current_loans = self.get_current_loans()
        if self.reader_type_info and len(current_loans) >= self.reader_type_info.max_books:
            return False, f"已达到最大借阅数量限制({self.reader_type_info.max_books}本)"

        # 检查是否有逾期未还图书
        overdue_loans = self.get_overdue_loans()
        if overdue_loans:
            return False, f"有{len(overdue_loans)}本图书逾期未还，请先归还"

        # 检查是否有未缴纳的罚款
        unpaid_fines = self.get_unpaid_fines()
        if unpaid_fines > 0:
            return False, f"有未缴纳罚款{unpaid_fines:.2f}元，请先缴纳"

        return True, "可以借阅"

    def get_unpaid_fines(self):
        """获取未缴纳的罚款总额"""
        from .loan import Loan
        unpaid_loans = Loan.query.filter_by(reader_id=self.reader_id, fine_status='unpaid').all()
        return sum(loan.fine_amount or 0 for loan in unpaid_loans)

    def get_status_display(self):
        """获取状态显示文本"""
        return '有效' if self.status == 'valid' else '失效'


# 管理员模型（不使用数据库）
class Admin(UserMixin):
    """管理员模型（硬编码）"""
    def __init__(self, username, password):
        self.id = 1  # 固定ID
        self.username = username
        self.password = password

    def __repr__(self):
        return f'<Admin {self.username}>'

    def verify_password(self, password):
        """验证密码"""
        return self.password == password

    def get_id(self):
        """获取用户ID"""
        return f"admin_{self.id}"


# 创建硬编码的管理员实例
admin_instance = Admin('admin', '1')


@login_manager.user_loader
def load_user(user_id):
    """加载用户"""
    # 检查是否为管理员
    if user_id.startswith('admin_'):
        # 返回硬编码的管理员实例
        return admin_instance
    # 检查是否为工作人员
    elif user_id.startswith('staff_'):
        from .staff import Staff
        staff_id = user_id.replace('staff_', '')
        return Staff.query.get(staff_id)
    # 否则为读者
    else:
        return Reader.query.get(user_id)
