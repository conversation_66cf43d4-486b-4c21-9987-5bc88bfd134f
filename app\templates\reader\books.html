{% extends 'base.html' %}

{% block title %}图书检索 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/reader.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <!-- 搜索卡片 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">图书检索</h6>
        </div>
        <div class="card-body">
            <form action="{{ url_for('reader.books') }}" method="get">
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" placeholder="输入书名、作者、出版社或ISBN..." name="q" value="{{ search_query }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" name="category">
                            <option value="">所有类别</option>
                            {% for category in categories %}
                            <option value="{{ category.book_category_id }}" {% if category_id == category.book_category_id %}selected{% endif %}>
                                {{ category.category_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 图书列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">图书列表</h6>
            <span class="text-muted">共找到 {{ pagination.total }} 本图书</span>
        </div>
        <div class="card-body">
            {% if books %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ISBN</th>
                            <th>书名</th>
                            <th>作者</th>
                            <th>出版社</th>
                            <th>出版年份</th>
                            <th>可借数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for book in books %}
                        <tr>
                            <td>{{ book.isbn }}</td>
                            <td>{{ book.title }}</td>
                            <td>{{ book.author }}</td>
                            <td>{{ book.publisher }}</td>
                            <td>{{ book.year }}</td>
                            <td>
                                {% if book.available_quantity > 0 %}
                                <span class="badge bg-success">{{ book.available_quantity }}</span>
                                {% else %}
                                <span class="badge bg-danger">0</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('reader.book_detail', isbn=book.isbn) }}" class="btn btn-sm btn-info">
                                    <i class="bi bi-info-circle"></i> 详情
                                </a>
                                {% if book.isbn in borrowed_books %}
                                <!-- 已借阅此图书，显示归还按钮 -->
                                <a href="{{ url_for('reader.return_book', loan_id=borrowed_books[book.isbn]) }}" class="btn btn-sm btn-warning">
                                    <i class="bi bi-journal-arrow-up"></i> 归还
                                </a>
                                {% elif book.available_quantity > 0 %}
                                <!-- 未借阅且有可借数量，显示借阅按钮 -->
                                <form action="{{ url_for('reader.borrow_book', isbn=book.isbn) }}" method="post" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-primary">
                                        <i class="bi bi-journal-plus"></i> 借阅
                                    </button>
                                </form>
                                {% else %}
                                <!-- 未借阅且无可借数量，显示禁用按钮 -->
                                <button class="btn btn-sm btn-secondary" disabled>
                                    <i class="bi bi-journal-x"></i> 暂不可借
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('reader.books', page=pagination.prev_num, q=search_query, category=category_id) }}">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="bi bi-chevron-left"></i></span>
                    </li>
                    {% endif %}

                    {% for page in pagination.iter_pages() %}
                        {% if page %}
                            {% if page != pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('reader.books', page=page, q=search_query, category=category_id) }}">{{ page }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('reader.books', page=pagination.next_num, q=search_query, category=category_id) }}">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="bi bi-chevron-right"></i></span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-search fa-4x text-gray-300 mb-3"></i>
                <p class="text-gray-500">未找到符合条件的图书</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
