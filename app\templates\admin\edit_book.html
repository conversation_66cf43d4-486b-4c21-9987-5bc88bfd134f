{% extends 'base.html' %}

{% block title %}编辑图书 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 编辑图书卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">编辑图书</h6>
                    <div>
                        <a href="{{ url_for('admin.book_detail', isbn=book.isbn) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回详情
                        </a>
                        <a href="{{ url_for('admin.books') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-list"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('admin.edit_book', isbn=book.isbn) }}" method="post">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="isbn" class="form-label">ISBN</label>
                                <input type="text" class="form-control" id="isbn" value="{{ book.isbn }}" readonly>
                                <div class="form-text">ISBN不可修改，作为唯一标识</div>
                            </div>
                            <div class="col-md-6">
                                <label for="title" class="form-label">书名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="{{ book.title }}" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="author" class="form-label">作者 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="author" name="author" value="{{ book.author }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="publisher" class="form-label">出版社 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="publisher" name="publisher" value="{{ book.publisher }}" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="year" class="form-label">出版年份 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="year" name="year" min="1000" max="9999" value="{{ book.year }}" required>
                            </div>
                            <div class="col-md-8">
                                <label for="book_category_id" class="form-label">图书类别 <span class="text-danger">*</span></label>
                                <select class="form-select" id="book_category_id" name="book_category_id" required>
                                    <option value="">请选择类别</option>
                                    {% for category in categories %}
                                    <option value="{{ category.book_category_id }}" {% if book.book_category_id == category.book_category_id %}selected{% endif %}>
                                        {{ category.category_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="quantity" class="form-label">总数量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="quantity" name="quantity" min="0" value="{{ book.quantity }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="available_quantity" class="form-label">可借数量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="available_quantity" name="available_quantity" min="0" value="{{ book.available_quantity }}" required>
                                <div class="form-text">可借数量不能超过总数量</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            // 验证可借数量不超过总数量
            const quantityInput = document.getElementById('quantity');
            const availableQuantityInput = document.getElementById('available_quantity');
            const quantityValue = parseInt(quantityInput.value);
            const availableQuantityValue = parseInt(availableQuantityInput.value);
            
            if (availableQuantityValue > quantityValue) {
                alert('可借数量不能超过总数量');
                availableQuantityInput.focus();
                event.preventDefault();
                return;
            }
        });
    });
</script>
{% endblock %}
