{% extends 'base.html' %}

{% block title %}读者仪表盘 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/reader.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<style>
    /* 统一读者仪表盘与管理员界面风格 */
    .welcome-card {
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        background-color: white;
        transition: all 0.3s ease;
    }

    .welcome-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .welcome-avatar {
        width: 60px;
        height: 60px;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        background: linear-gradient(135deg, #1a3a5f, #4299e1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }

    .welcome-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
    }

    .welcome-subtitle {
        font-size: 0.9rem;
        color: #718096;
    }

    .book-recommendation-card {
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .book-recommendation-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .book-recommendation-card .card-header {
        background-color: white;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 0.75rem 1rem;
    }

    .book-recommendation-card .card-title {
        font-weight: 600;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        color: #2d3748;
        font-size: 0.9rem;
    }

    .book-recommendation-card .card-title i {
        margin-right: 0.5rem;
        color: #4299e1;
        font-size: 1rem;
    }

    .book-recommendation-item {
        border-radius: 0.75rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        height: 100%;
    }

    .book-recommendation-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .book-icon {
        width: 50px;
        height: 50px;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        background: linear-gradient(135deg, #1a3a5f, #4299e1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }

    .table-reader {
        margin-bottom: 0;
    }

    .table-reader thead th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.7rem;
        letter-spacing: 0.05rem;
        padding: 0.6rem 1rem;
        border-top: none;
        border-bottom-width: 1px;
        color: #4a5568;
    }

    .table-reader tbody td {
        padding: 0.6rem 1rem;
        vertical-align: middle;
        border-color: rgba(0, 0, 0, 0.05);
    }

    .table-reader.table-hover tbody tr:hover {
        background-color: rgba(66, 153, 225, 0.05);
    }

    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 50rem;
        font-weight: 500;
        font-size: 0.7rem;
        display: inline-flex;
        align-items: center;
    }

    .badge-success {
        background-color: rgba(72, 187, 120, 0.1);
        color: #48bb78;
    }

    .badge-danger {
        background-color: rgba(245, 101, 101, 0.1);
        color: #f56565;
    }
</style>
{% endblock %}

{% block page_title %}仪表盘{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid px-3 py-2 dashboard-optimized">
    <!-- 欢迎卡片 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="welcome-card slide-in-up">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="welcome-avatar me-3">
                            <i class="bi bi-person-circle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="welcome-title mb-1">欢迎回来，{{ current_user.name }}！</h5>
                            <p class="welcome-subtitle mb-0">
                                {{ current_user.reader_type_info.reader_type }} · {{ current_user.department }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 借阅统计卡片 -->
    <div class="row mb-3">
        <!-- 当前借阅 -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="dashboard-card primary slide-in-up">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="card-icon primary me-3">
                        <i class="bi bi-journal-text"></i>
                    </div>
                    <div class="text-start flex-grow-1">
                        <div class="card-title mb-1">当前借阅</div>
                        <div class="dashboard-stat">{{ current_borrowed }}</div>
                        <div class="stat-trend text-muted">
                            <small>已借阅图书数量</small>
                        </div>
                    </div>
                </div>
                <div class="card-progress">
                    <div class="progress-bar bg-primary" style="width: {{ (current_borrowed / max_books * 100) if max_books > 0 else 0 }}%"></div>
                </div>
            </div>
        </div>

        <!-- 可借数量 -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="dashboard-card success slide-in-up" style="animation-delay: 0.1s;">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="card-icon success me-3">
                        <i class="bi bi-journal-plus"></i>
                    </div>
                    <div class="text-start flex-grow-1">
                        <div class="card-title mb-1">可借数量</div>
                        <div class="dashboard-stat">{{ available_to_borrow }}</div>
                        <div class="stat-trend text-muted">
                            <small>还可借阅图书数量</small>
                        </div>
                    </div>
                </div>
                <div class="card-progress">
                    <div class="progress-bar bg-success" style="width: {{ (available_to_borrow / max_books * 100) if max_books > 0 else 0 }}%"></div>
                </div>
            </div>
        </div>

        <!-- 最大可借 -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="dashboard-card info slide-in-up" style="animation-delay: 0.2s;">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="card-icon info me-3">
                        <i class="bi bi-book"></i>
                    </div>
                    <div class="text-start flex-grow-1">
                        <div class="card-title mb-1">最大可借</div>
                        <div class="dashboard-stat">{{ max_books }}</div>
                        <div class="stat-trend text-muted">
                            <small>最大借阅图书数量</small>
                        </div>
                    </div>
                </div>
                <div class="card-progress">
                    <div class="progress-bar bg-info" style="width: 100%"></div>
                </div>
            </div>
        </div>

        <!-- 逾期数量 -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="dashboard-card warning slide-in-up" style="animation-delay: 0.3s;">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="card-icon warning me-3">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                    </div>
                    <div class="text-start flex-grow-1">
                        <div class="card-title mb-1">逾期数量</div>
                        <div class="dashboard-stat">{{ overdue_loans|length }}</div>
                        <div class="stat-trend text-muted">
                            <small>已逾期图书数量</small>
                        </div>
                    </div>
                </div>
                <div class="card-progress">
                    <div class="progress-bar bg-warning" style="width: {{ (overdue_loans|length / current_borrowed * 100) if current_borrowed > 0 else 0 }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 当前借阅 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="chart-card fade-in">
                <div class="card-header">
                    <h6 class="card-title">
                        <i class="bi bi-journal-bookmark"></i>
                        当前借阅
                    </h6>
                    <div class="card-actions">
                        <a href="{{ url_for('reader.loans') }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-list-ul me-1"></i>查看全部
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if current_loans %}
                    <div class="table-responsive">
                        <table class="table table-reader table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>图书名称</th>
                                    <th>借阅日期</th>
                                    <th>应还日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for loan in current_loans[:5] %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-xs me-2 bg-primary text-white rounded-circle">
                                                {{ loan.book.title[0] }}
                                            </div>
                                            <a href="{{ url_for('reader.book_detail', isbn=loan.book.isbn) }}" class="text-decoration-none">
                                                <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ loan.book.title }}">
                                                    {{ loan.book.title }}
                                                </span>
                                            </a>
                                        </div>
                                    </td>
                                    <td>{{ loan.loan_date|datetime('%Y-%m-%d') }}</td>
                                    <td>{{ loan.due_date|datetime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if loan.is_overdue() %}
                                        <span class="status-badge badge-danger">
                                            <i class="bi bi-exclamation-circle me-1"></i>已逾期
                                        </span>
                                        {% else %}
                                        <span class="status-badge badge-success">
                                            <i class="bi bi-check-circle me-1"></i>正常
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if not loan.is_overdue() %}
                                        <form action="{{ url_for('reader.renew_loan', loan_id=loan.loan_id) }}" method="post" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                            <button type="submit" class="btn btn-sm btn-primary">
                                                <i class="bi bi-arrow-clockwise me-1"></i>续借
                                            </button>
                                        </form>
                                        {% endif %}
                                        <a href="{{ url_for('reader.return_book', loan_id=loan.loan_id) }}" class="btn btn-sm btn-warning">
                                            <i class="bi bi-journal-arrow-up me-1"></i>归还
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="p-2 text-end">
                        <small class="text-muted">显示最近 {{ current_loans[:5]|length }} 条借阅记录</small>
                    </div>
                    {% else %}
                    <div class="empty-state py-5">
                        <i class="bi bi-journal-text text-muted"></i>
                        <h6 class="mt-3 mb-2">您当前没有借阅任何图书</h6>
                        <p class="text-muted small mb-3">您可以浏览图书馆的藏书并借阅感兴趣的图书</p>
                        <a href="{{ url_for('reader.books') }}" class="btn btn-primary">
                            <i class="bi bi-search me-1"></i>查找图书
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 推荐图书 -->
    <div class="row">
        <div class="col-12">
            <div class="chart-card fade-in" style="animation-delay: 0.1s;">
                <div class="card-header">
                    <h6 class="card-title">
                        <i class="bi bi-stars"></i>
                        推荐图书
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="book-recommendation-item p-3">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="book-icon me-3">
                                        <i class="bi bi-book"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">计算机科学导论</h6>
                                        <p class="text-muted small mb-0">托马斯·科尔曼</p>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <a href="{{ url_for('reader.books') }}?q=计算机科学导论" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-search me-1"></i>查看详情
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="book-recommendation-item p-3">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="book-icon me-3">
                                        <i class="bi bi-book"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">人工智能：一种现代方法</h6>
                                        <p class="text-muted small mb-0">斯图尔特·罗素</p>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <a href="{{ url_for('reader.books') }}?q=人工智能" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-search me-1"></i>查看详情
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="book-recommendation-item p-3">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="book-icon me-3">
                                        <i class="bi bi-book"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">数据结构与算法分析</h6>
                                        <p class="text-muted small mb-0">马克·艾伦·韦斯</p>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <a href="{{ url_for('reader.books') }}?q=数据结构" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-search me-1"></i>查看详情
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
