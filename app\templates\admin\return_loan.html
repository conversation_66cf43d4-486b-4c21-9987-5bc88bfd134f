{% extends 'base.html' %}

{% block title %}归还图书 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 归还图书卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">归还图书</h6>
                    <div>
                        <a href="{{ url_for('admin.loan_detail', loan_id=loan.loan_id) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回详情
                        </a>
                        <a href="{{ url_for('admin.loans') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-list"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="bi bi-info-circle me-2"></i>
                        您正在处理图书归还，请确认以下信息无误。
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">读者信息</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1"><strong>读者ID:</strong> {{ loan.reader_id }}</p>
                                    <p class="mb-1"><strong>姓名:</strong> {{ loan.reader.name }}</p>
                                    <p class="mb-0"><strong>类型:</strong> {{ loan.reader.reader_type }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">图书信息</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1"><strong>ISBN:</strong> {{ loan.isbn }}</p>
                                    <p class="mb-1"><strong>书名:</strong> {{ loan.book.title }}</p>
                                    <p class="mb-0"><strong>作者:</strong> {{ loan.book.author }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">借阅信息</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1"><strong>借阅日期:</strong> {{ loan.loan_date|datetime('%Y-%m-%d') }}</p>
                                    <p class="mb-1"><strong>应还日期:</strong> {{ loan.due_date|datetime('%Y-%m-%d') }}</p>
                                    <p class="mb-0">
                                        <strong>状态:</strong>
                                        {% if loan.is_overdue() %}
                                        <span class="badge bg-danger">已逾期</span>
                                        {% else %}
                                        <span class="badge bg-success">正常</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">罚款信息</h6>
                                </div>
                                <div class="card-body">
                                    {% if loan.is_overdue() %}
                                    <p class="mb-1"><strong>逾期天数:</strong> {{ loan.get_overdue_days() }} 天</p>
                                    <p class="mb-1"><strong>罚款金额:</strong> <span class="text-danger">{{ loan.calculate_fine() }} 元</span></p>
                                    <p class="mb-0"><strong>罚款标准:</strong> {{ loan.book.category.fine_amount }} 元/天</p>
                                    {% else %}
                                    <p class="mb-0">图书未逾期，无需缴纳罚款。</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <form action="{{ url_for('admin.return_loan', loan_id=loan.loan_id) }}" method="post">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        {% if loan.is_overdue() %}
                        <div class="alert alert-warning mb-4">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            此图书已逾期 {{ loan.get_overdue_days() }} 天，需缴纳罚款 {{ loan.calculate_fine() }} 元。
                            请确认读者已缴纳罚款后再确认归还。
                        </div>
                        {% endif %}

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('admin.loan_detail', loan_id=loan.loan_id) }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-journal-check"></i> 确认归还
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
