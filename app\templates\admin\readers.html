{% extends 'base.html' %}

{% block title %}读者管理 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid px-3 py-2 dashboard-optimized">
    <!-- 搜索卡片 -->
    <div class="chart-card mb-3 zoom-in">
        <div class="card-header">
            <h6 class="card-title">
                <i class="bi bi-search"></i>
                读者搜索
            </h6>
            <div class="card-actions">
                <a href="{{ url_for('admin.create_reader') }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-person-plus"></i> 添加读者
                </a>
            </div>
        </div>
        <div class="card-body">
            <form action="{{ url_for('admin.readers') }}" method="get" class="search-form">
                <div class="row g-3">
                    <div class="col-md-7">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-search text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 ps-0"
                                placeholder="输入读者编号、姓名、学院或邮箱..."
                                name="q" value="{{ search_query }}"
                                aria-label="搜索读者">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="type" aria-label="读者类型">
                            <option value="">所有类型</option>
                            {% for type in reader_types %}
                            <option value="{{ type.reader_type }}" {% if reader_type == type.reader_type %}selected{% endif %}>
                                {{ type.reader_type }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100" type="submit">
                            <i class="bi bi-search me-1"></i> 搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 读者列表 -->
    <div class="chart-card mb-3 fade-in">
        <div class="card-header">
            <h6 class="card-title">
                <i class="bi bi-people"></i>
                读者列表
            </h6>
            <div class="card-actions">
                <span class="badge bg-primary rounded-pill">
                    共 {{ pagination.total }} 名读者
                </span>
            </div>
        </div>
        <div class="card-body p-0">
            {% if readers %}
            <div class="table-responsive">
                <table class="table table-admin table-hover mb-0" id="readersTable">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="id">
                                读者编号 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="name">
                                姓名 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="type">
                                读者类型 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="department">
                                学院/部门 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="phone">
                                电话 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="email">
                                邮箱 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="date">
                                注册日期 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for reader in readers %}
                        <tr class="reader-row">
                            <td>
                                <span class="text-monospace">{{ reader.reader_id }}</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-xs me-2 bg-primary text-white rounded-circle">
                                        {{ reader.name[0] }}
                                    </div>
                                    <span>{{ reader.name }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge badge-primary">
                                    {{ reader.reader_type }}
                                </span>
                            </td>
                            <td>{{ reader.department }}</td>
                            <td>{{ reader.phone }}</td>
                            <td>
                                <span class="text-truncate d-inline-block" style="max-width: 150px;" title="{{ reader.email }}">
                                    {{ reader.email }}
                                </span>
                            </td>
                            <td>{{ reader.registration_date|datetime('%Y-%m-%d') }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('admin.reader_detail', reader_id=reader.reader_id) }}"
                                       class="btn btn-sm btn-outline-info"
                                       data-bs-toggle="tooltip"
                                       title="查看详情">
                                        <i class="bi bi-info-circle"></i>
                                    </a>
                                    <a href="{{ url_for('admin.edit_reader', reader_id=reader.reader_id) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       data-bs-toggle="tooltip"
                                       title="编辑读者">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <div class="table-pagination p-3 d-flex justify-content-between align-items-center">
                <div class="pagination-info">
                    {% set start_item = (pagination.page - 1) * pagination.per_page + 1 %}
                    {% set end_item = pagination.page * pagination.per_page %}
                    {% if end_item > pagination.total %}
                        {% set end_item = pagination.total %}
                    {% endif %}
                    显示 {{ start_item }} 至 {{ end_item }} 条，
                    共 {{ pagination.total }} 条记录
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm mb-0">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.readers', page=pagination.prev_num, q=search_query, type=reader_type) }}" aria-label="上一页">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link"><i class="bi bi-chevron-left"></i></span>
                        </li>
                        {% endif %}

                        {% for page in pagination.iter_pages() %}
                            {% if page %}
                                {% if page != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.readers', page=page, q=search_query, type=reader_type) }}">{{ page }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.readers', page=pagination.next_num, q=search_query, type=reader_type) }}" aria-label="下一页">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link"><i class="bi bi-chevron-right"></i></span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <div class="empty-state">
                    <i class="bi bi-people display-4 text-muted mb-3"></i>
                    <h5 class="text-muted">未找到符合条件的读者</h5>
                    <p class="text-muted mb-3">尝试使用不同的搜索条件或添加新读者</p>
                    <a href="{{ url_for('admin.create_reader') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus me-1"></i> 添加读者
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化工具提示
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                delay: { show: 300, hide: 100 }
            });
        });

        // 表格排序功能
        const table = document.getElementById('readersTable');
        if (table) {
            const headers = table.querySelectorAll('th.sortable');

            headers.forEach(header => {
                header.addEventListener('click', function() {
                    const sortBy = this.getAttribute('data-sort');
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));

                    // 切换排序方向
                    const isAscending = !this.classList.contains('sort-asc');

                    // 移除所有排序类
                    headers.forEach(h => {
                        h.classList.remove('sort-asc', 'sort-desc');
                    });

                    // 添加当前排序类
                    this.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

                    // 获取列索引
                    const columnIndex = Array.from(this.parentNode.children).indexOf(this);

                    // 排序行
                    rows.sort((a, b) => {
                        const aValue = a.children[columnIndex].textContent.trim();
                        const bValue = b.children[columnIndex].textContent.trim();

                        if (!isNaN(aValue) && !isNaN(bValue)) {
                            return isAscending ? aValue - bValue : bValue - aValue;
                        }

                        return isAscending
                            ? aValue.localeCompare(bValue, 'zh-CN')
                            : bValue.localeCompare(aValue, 'zh-CN');
                    });

                    // 重新添加排序后的行
                    rows.forEach(row => tbody.appendChild(row));
                });
            });
        }

        // 行悬停效果增强
        const readerRows = document.querySelectorAll('.reader-row');
        readerRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transition = 'background-color 0.2s ease';
            });
        });
    });
</script>
{% endblock %}
