-- 系统管理员默认  账户：admin  密码：1

-- 创建数据库
CREATE DATABASE IF NOT EXISTS LibraryDB;
USE LibraryDB;

-- 创建表
-- 读者类型表 (ReadersType)
CREATE TABLE ReadersType (
    ReaderType VARCHAR(20) NOT NULL PRIMARY KEY,  -- 读者类型（学生/教师）
    MaxBooks INT,                                 -- 可借阅图书数量上限
    BorrowDuration INT,                           -- 借阅期限（天数）
    MaxRenewals INT                               -- 续借次数
);

-- 读者表 (Readers)
CREATE TABLE Readers (
    ReaderID VARCHAR(10) NOT NULL PRIMARY KEY,     -- 读者编号（可以是学号或教工号）
    Name VARCHAR(50),                               -- 姓名
    Department VARCHAR(100),                        -- 学院
    Phone VARCHAR(15),                              -- 手机号
    Email VARCHAR(100),                             -- 电子邮箱
    ReaderType VARCHAR(20) NOT NULL,                -- 读者类型（学生/教师）
    RegistrationDate DATE DEFAULT (CURRENT_DATE),   -- 注册日期（默认当前时间）
    Pwd VARCHAR(20) NOT NULL DEFAULT '123456',      -- 密码    
    FOREIGN KEY (ReaderType) REFERENCES ReadersType(ReaderType) ON DELETE CASCADE
);

-- 图书类别表 (BookCategory)
CREATE TABLE BookCategory (
    BookCategoryID VARCHAR(10) NOT NULL PRIMARY KEY, -- 图书类别
    CategoryName VARCHAR(50) NOT NULL,               -- 图书类别名称
    FineAmount FLOAT DEFAULT 0.1                     -- 罚款金额（默认0.1元每天）
);

-- 图书信息表 (Books)
CREATE TABLE Books (
    ISBN VARCHAR(20) NOT NULL PRIMARY KEY,          -- ISBN号
    Title VARCHAR(255) NOT NULL,                    -- 书名
    Author VARCHAR(100) NOT NULL,                   -- 作者
    Publisher VARCHAR(100) NOT NULL,                -- 出版社
    Year INT NOT NULL,                              -- 出版年份
    BookCategoryID VARCHAR(10) NOT NULL,            -- 分类号
    Quantity INT NOT NULL,                          -- 库存数量
    AvailableQuantity INT,                          -- 可借数量
    FOREIGN KEY (BookCategoryID) REFERENCES BookCategory(BookCategoryID)
);

-- 借阅信息表 (Loans)
CREATE TABLE Loans (
    LoanID INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    ReaderID VARCHAR(10) NOT NULL,                  -- 读者编号（教师或学生ID）
    ISBN VARCHAR(20) NOT NULL,                      -- 图书ISBN号
    LoanDate DATETIME NOT NULL,                     -- 借阅时间
    DueDate DATETIME NOT NULL,                      -- 应归还时间
    ReturnDate DATETIME,                            -- 实际归还时间
    FineAmount DECIMAL(10, 2),                      -- 罚款金额
    FOREIGN KEY (ReaderID) REFERENCES Readers(ReaderID) ON DELETE CASCADE,
    FOREIGN KEY (ISBN) REFERENCES Books(ISBN) ON DELETE CASCADE
);




-- 插入读者类型数据
INSERT INTO ReadersType (ReaderType, MaxBooks, BorrowDuration, MaxRenewals)
VALUES
('学生', 5, 30, 2),          -- 学生类型，最多借5本书，借阅30天，最多续借2次
('教师', 10, 60, 5);         -- 教师类型，最多借10本书，借阅60天，最多续借5次

-- 学生数据插入
INSERT INTO Readers (ReaderID, Name, Department, Phone, Email, ReaderType, RegistrationDate)
VALUES
('S01', '曹鑫', '计算机', '13800000001', '<EMAIL>', '学生', CURRENT_DATE),
('S02', '赖宝韵', '大数据技术', '13800000002', '<EMAIL>', '学生', CURRENT_DATE),
('S03', '李洪佳', '大数据技术', '13800000003', '<EMAIL>', '学生', CURRENT_DATE),
('S04', '谢心怡', '大数据技术', '13800000004', '<EMAIL>', '学生', CURRENT_DATE),
('S05', '黄瑜嘉', '大数据技术', '13800000005', '<EMAIL>', '学生', CURRENT_DATE),
('S06', '陈烨恒', '大数据技术', '13800000006', '<EMAIL>', '学生', CURRENT_DATE),
('S07', '钟子蓉', '大数据技术', '13800000007', '<EMAIL>', '学生', CURRENT_DATE),
('S08', '桂雨晨', '大数据技术', '13800000008', '<EMAIL>', '学生', CURRENT_DATE),
('S09', '陈羲池', '大数据技术', '13800000013', '<EMAIL>', '学生', CURRENT_DATE),
('S10', '吴瑾', '大数据技术', '13800000014', '<EMAIL>', '学生', CURRENT_DATE),
('S11', '郑舒尹', '大数据技术', '13800000015', '<EMAIL>', '学生', CURRENT_DATE),
('S12', '林光煜', '大数据技术', '13800000033', '<EMAIL>', '学生', CURRENT_DATE),
('S13', '周钰', '计算机', '13800000034', '<EMAIL>', '学生', CURRENT_DATE),
('S14', '蒋政豪', '大数据技术', '13800000035', '<EMAIL>', '学生', CURRENT_DATE),
('S15', '林富兴', '大数据技术', '13800000036', '<EMAIL>', '学生', CURRENT_DATE);

-- 教师数据插入
INSERT INTO Readers (ReaderID, Name, Department, Phone, Email, ReaderType, RegistrationDate)
VALUES
('T01', '蔡明', '计算机学院', '13900000001', '<EMAIL>', '教师', CURRENT_DATE),
('T02', '薛春艳', '计算机学院', '13900000002', '<EMAIL>', '教师', CURRENT_DATE),
('T03', '李俊峰', '大数据技术学院', '13900000003', '<EMAIL>', '教师', CURRENT_DATE),
('T04', '陈俊仁', '计算机学院', '13900000004', '<EMAIL>', '教师', CURRENT_DATE),
('T05', '付爽', '大数据技术学院', '13900000005', '<EMAIL>', '教师', CURRENT_DATE),
('T06', '陈宁伟', '大数据技术学院', '13900000006', '<EMAIL>', '教师', CURRENT_DATE),
('T07', '魏滢', '计算机学院', '13900000007', '<EMAIL>', '教师', CURRENT_DATE),
('T08', '陈俊仁', '大数据技术学院', '13900000008', '<EMAIL>', '教师', CURRENT_DATE),
('T09', '林智鹏', '大数据技术学院', '13900000009', '<EMAIL>', '教师', CURRENT_DATE),
('T10', '周书伟', '计算机学院', '13900000010', '<EMAIL>', '教师', CURRENT_DATE);

-- 插入图书类别数据
INSERT INTO BookCategory (BookCategoryID, CategoryName, FineAmount)
VALUES
('C1001', '计算机科学', 0.1),   -- 计算机科学类别，每天罚款0.1元
('C1002', '人工智能', 0.1),     -- 人工智能类别，每天罚款0.1元
('C1003', '数据科学与大数据', 0.1), -- 数据科学与大数据类别，每天罚款0.1元
('C1004', '编程语言', 0.1),     -- 编程语言类别，每天罚款0.1元
('C1005', '计算机网络与通信', 0.1), -- 计算机网络与通信类别，每天罚款0.1元
('C1006', '操作系统与硬件', 0.1), -- 操作系统与硬件类别，每天罚款0.1元
('C1007', '数据库', 0.1),       -- 数据库类别，每天罚款0.1元
('C1008', '软件工程与项目管理', 0.1), -- 软件工程与项目管理类别，每天罚款0.1元
('C1009', '网络安全与加密技术', 0.1), -- 网络安全与加密技术类别，每天罚款0.1元
('C1010', '计算机图形学与多媒体', 0.1); -- 计算机图形学与多媒体类别，每天罚款0.1元

-- 插入计算机相关图书数据
INSERT INTO Books (ISBN, Title, Author, Publisher, Year, BookCategoryID, Quantity, AvailableQuantity)
VALUES
('978-7-121-31299-5', '计算机网络', '谢希仁', '清华大学出版社', 2020, 'C1001', 10, 8),
('978-7-111-56117-4', '电子工程基础', '张立新', '电子工业出版社', 2018, 'C1002', 15, 12),
('978-7-04-038201-4', '有机化学', '周自恒', '高等教育出版社', 2019, 'C1003', 20, 18),
('978-7-04-040478-2', '高等数学', '同济大学', '同济大学出版社', 2021, 'C1004',30, 25),
('978-7-121-24999-0', '数据结构', '严蔚敏', '清华大学出版社', 2021, 'C1001', 50, 48),
('978-7-03-037365-7', '计算机组成与设计', 'David A. Patterson', '机械工业出版社', 2020, 'C1001', 30, 28),
('978-7-111-64761-4', '操作系统概念', 'Abraham Silberschatz', '高等教育出版社', 2019, 'C1001',  25, 22),
('978-7-04-039419-1', '软件工程', 'Roger S. Pressman', '人民邮电出版社', 2020, 'C1002', 40, 35),
('978-7-04-036080-7', '计算机网络原理', 'Tanenbaum', '电子工业出版社', 2017, 'C1001', 15, 13),
('978-7-04-037420-1', '数据库系统概论', '王珊', '高等教育出版社', 2018, 'C1002', 35, 30),
('978-7-03-028311-2', '人工智能：一种现代的方法', 'Stuart Russell', '机械工业出版社', 2020, 'C1002', 40, 38),
('978-7-04-040227-7', '机器学习', '周志华', '清华大学出版社', 2019, 'C1002',50, 45),
('978-7-121-23890-2', '深度学习', 'Ian Goodfellow', '人民邮电出版社', 2018, 'C1002', 60, 58),
('978-7-121-31881-7', '自然语言处理入门', 'Jacob Eisenstein', '机械工业出版社', 2021, 'C1002', 25, 22),
('978-7-04-040019-8', 'Python编程基础', 'Zed Shaw', '人民邮电出版社', 2021, 'C1004', 80, 75),
('978-7-111-64897-7', 'C++编程基础', 'Bjarne Stroustrup', '机械工业出版社', 2019, 'C1004', 40, 35),
('978-7-111-69999-8', 'Java核心技术', 'Cay S. Horstmann', '人民邮电出版社', 2021, 'C1004',  50, 48),
('978-7-04-029557-9', '数据科学导论', 'Joel Grus', '电子工业出版社', 2020, 'C1003', 30, 28),
('978-7-04-039763-9', '大数据分析', 'Bill Franks', '高等教育出版社', 2021, 'C1003', 35, 32),
('978-7-04-037347-1', 'Hadoop权威指南', 'Tom White', '人民邮电出版社', 2021, 'C1003',  60, 55),
('978-7-04-041973-0', '图算法：数据结构与应用', 'Robert Sedgewick', '清华大学出版社', 2022, 'C1001', 45, 40),
('978-7-04-039246-7', '人工智能：智能系统设计', 'George F. Luger', '电子工业出版社', 2022, 'C1002', 30, 28),
('978-7-12-030354-8', '机器学习与数据挖掘', '周志华', '北京大学出版社', 2019, 'C1002', 50, 48),
('978-7-111-59175-2', '高性能计算机架构', 'David A. Patterson', '机械工业出版社', 2021, 'C1006', 20, 18),
('978-7-04-038215-7', '操作系统设计与实现', 'Andrew S. Tanenbaum', '人民邮电出版社', 2020, 'C1006',35, 30),
('978-7-121-31233-9', '深入理解计算机系统', 'Randal E. Bryant', '清华大学出版社', 2021, 'C1006',  40, 38),
('978-7-111-31415-1', '数据库系统实现', 'Hector Garcia-Molina', '清华大学出版社', 2020, 'C1007', 50, 45),
('978-7-121-32645-8', 'MySQL数据库管理与优化', '高建', '电子工业出版社', 2021, 'C1007', 30, 28),
('978-7-04-034485-2', 'NoSQL数据库', 'Dan Sullivan', '机械工业出版社', 2020, 'C1007', 40, 35),
('978-7-111-51012-1', '现代数据库系统', 'Jeffrey D. Ullman', '高等教育出版社', 2019, 'C1007', 30, 28),
('978-7-121-25563-7', 'JavaScript高级程序设计', 'Nicholas C. Zakas', '人民邮电出版社', 2019, 'C1004', 60, 58),
('978-7-111-53044-9', 'Node.js实战', 'Mike Cantelon', '电子工业出版社', 2020, 'C1004', 50, 48),
('978-7-111-65728-5', 'React开发指南', 'Kadi Bismuth', '人民邮电出版社', 2020, 'C1004', 35, 33),
('978-7-03-038429-4', '计算机图形学：原理与实践', 'John F. Hughes', '机械工业出版社', 2021, 'C1010',45, 42),
('978-7-121-31365-7', 'OpenGL编程指南', 'Dave Shreiner', '清华大学出版社', 2018, 'C1010',50, 48),
('978-7-111-64844-1', '深入理解图形学', 'David M. Mount', '电子工业出版社', 2021, 'C1010',40, 38),
('978-7-121-32874-2', 'CUDA并行程序设计', 'Yuan Xie', '清华大学出版社', 2022, 'C1010', 25, 22),
('978-7-121-23315-0', '深度学习与计算机视觉', 'Rajalingappaa Shanmugamani', '电子工业出版社', 2020, 'C1010',30, 27),
('978-7-111-65999-0', '图像处理与计算机视觉', 'Richard Szeliski', '机械工业出版社', 2019, 'C1010', 35, 32),
('978-7-04-041523-7', '计算机视觉：算法与应用', 'Richard Szeliski', '人民邮电出版社', 2021, 'C1010', 40, 36),
('978-7-04-039825-5', 'Python深度学习', 'Francois Chollet', '人民邮电出版社', 2021, 'C1002', 50, 48),
('978-7-03-030482-6', '机器学习与数据挖掘实用指南', 'Ronald C. Ross', '电子工业出版社', 2020, 'C1002',30, 28),
('978-7-04-037520-4', '深度学习与强化学习', 'Richard S. Sutton', '清华大学出版社', 2022, 'C1002',40, 38),
('978-7-111-63267-6', '数据挖掘与机器学习基础', 'Shu-Ching Chen', '机械工业出版社', 2021, 'C1003', 25, 23),
('978-7-03-029482-0', '机器学习实战', 'Peter Harrington', '人民邮电出版社', 2019, 'C1002', 40, 38),
('978-7-04-032968-9', 'Python数据分析与数据挖掘', 'Wes McKinney', '清华大学出版社', 2020, 'C1003', 50, 45),
('978-7-04-036040-0', '大数据处理技术', 'Tom White', '人民邮电出版社', 2021, 'C1003', 35, 33),
('978-7-04-032055-2', '大数据与人工智能', 'Jure Leskovec', '机械工业出版社', 2021, 'C1003', 60, 57);


-- 插入借阅数据
INSERT INTO Loans (ReaderID, ISBN, LoanDate, DueDate, ReturnDate, FineAmount)
VALUES
-- 学生借阅示例
('S01', '978-7-121-31299-5', DATE_SUB(CURRENT_DATE, INTERVAL 10 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 10 DAY), INTERVAL 20 DAY), NULL, 0),
('S02', '978-7-111-56117-4', DATE_SUB(CURRENT_DATE, INTERVAL 8 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 8 DAY), INTERVAL 20 DAY), NULL, 0),
('S03', '978-7-04-038201-4', DATE_SUB(CURRENT_DATE, INTERVAL 15 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 15 DAY), INTERVAL 20 DAY), NULL, 0),
('S04', '978-7-04-040478-2', DATE_SUB(CURRENT_DATE, INTERVAL 5 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 5 DAY), INTERVAL 20 DAY), NULL, 0),
('S05', '978-7-121-24999-0', DATE_SUB(CURRENT_DATE, INTERVAL 50 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 20 DAY), INTERVAL 20 DAY), NULL, 0),
('S06', '978-7-03-037365-7', DATE_SUB(CURRENT_DATE, INTERVAL 12 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 12 DAY), INTERVAL 20 DAY), NULL, 0),
('S07', '978-7-111-64761-4', DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY), INTERVAL 20 DAY), NULL, 0),
('S08', '978-7-04-039419-1', DATE_SUB(CURRENT_DATE, INTERVAL 9 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 9 DAY), INTERVAL 20 DAY), NULL, 0),
('S09', '978-7-04-036080-7', DATE_SUB(CURRENT_DATE, INTERVAL 18 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 18 DAY), INTERVAL 20 DAY), NULL, 0),
('S10', '978-7-04-037420-1', DATE_SUB(CURRENT_DATE, INTERVAL 11 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 11 DAY), INTERVAL 20 DAY), NULL, 0),
('S07', '978-7-03-028311-2', DATE_SUB(CURRENT_DATE, INTERVAL 13 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 13 DAY), INTERVAL 20 DAY), NULL, 0),
('S07', '978-7-121-31881-7', DATE_SUB(CURRENT_DATE, INTERVAL 16 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 16 DAY), INTERVAL 20 DAY), NULL, 0),
('S01', '978-7-121-23890-2', DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY), INTERVAL 20 DAY), NULL, 0),
('S03', '978-7-121-25563-7', DATE_SUB(CURRENT_DATE, INTERVAL 17 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 17 DAY), INTERVAL 20 DAY), NULL, 0),
('S02', '978-7-111-51012-1', DATE_SUB(CURRENT_DATE, INTERVAL 19 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 19 DAY), INTERVAL 20 DAY), NULL, 0),
-- 教师借阅示例
('T01', '978-7-04-034485-2', DATE_SUB(CURRENT_DATE, INTERVAL 22 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 22 DAY), INTERVAL 60 DAY), NULL, 0),
('T02', '978-7-111-53044-9', DATE_SUB(CURRENT_DATE, INTERVAL 25 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 25 DAY), INTERVAL 60 DAY), NULL, 0),
('T03', '978-7-121-32645-8', DATE_SUB(CURRENT_DATE, INTERVAL 23 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 23 DAY), INTERVAL 60 DAY), NULL, 0),
('T04', '978-7-111-65728-5', DATE_SUB(CURRENT_DATE, INTERVAL 21 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 21 DAY), INTERVAL 60 DAY), NULL, 0),
('T05', '978-7-121-32874-2', DATE_SUB(CURRENT_DATE, INTERVAL 24 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 24 DAY), INTERVAL 60 DAY), NULL, 0),
('T06', '978-7-111-64844-1', DATE_SUB(CURRENT_DATE, INTERVAL 26 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 26 DAY), INTERVAL 60 DAY), NULL, 0),
('T07', '978-7-121-31365-7', DATE_SUB(CURRENT_DATE, INTERVAL 27 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 27 DAY), INTERVAL 60 DAY), NULL, 0),
('T08', '978-7-111-65999-0', DATE_SUB(CURRENT_DATE, INTERVAL 28 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 28 DAY), INTERVAL 60 DAY), NULL, 0),
('T09', '978-7-121-31233-9', DATE_SUB(CURRENT_DATE, INTERVAL 29 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 29 DAY), INTERVAL 60 DAY), NULL, 0),
('T10', '978-7-111-31415-1', DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY), INTERVAL 60 DAY), NULL, 0),
('T01', '978-7-04-040227-7', DATE_SUB(CURRENT_DATE, INTERVAL 5 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 5 DAY), INTERVAL 20 DAY), NULL, 0),
('T01', '978-7-121-23890-2', DATE_SUB(CURRENT_DATE, INTERVAL 8 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 8 DAY), INTERVAL 60 DAY), NULL, 0),
('T03', '978-7-111-64761-4', DATE_SUB(CURRENT_DATE, INTERVAL 12 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 12 DAY), INTERVAL 20 DAY), NULL, 0),
('T10', '978-7-03-028311-2', DATE_SUB(CURRENT_DATE, INTERVAL 15 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 15 DAY), INTERVAL 20 DAY), NULL, 0),
('T05', '978-7-04-036080-7', DATE_SUB(CURRENT_DATE, INTERVAL 20 DAY), DATE_ADD(DATE_SUB(CURRENT_DATE, INTERVAL 20 DAY), INTERVAL 60 DAY), NULL, 0);





-- 视图
-- 1. 读者当前借阅及逾期情况视图：显示每位读者当前借阅的图书、归还日期及是否逾期、罚款金额。
CREATE VIEW ReaderCurrentBorrowStatus AS
SELECT 
    r.ReaderID, 
    r.Name AS ReaderName, 
    b.Title AS BookTitle, 
    b.ISBN, 
    l.DueDate, 
    l.ReturnDate,
    CASE 
        WHEN l.ReturnDate IS NULL AND DATEDIFF(l.DueDate, CURRENT_DATE) > 0 THEN '逾期'
        ELSE '正常'
    END AS BorrowStatus,
    l.FineAmount
FROM Loans l
JOIN Readers r ON l.ReaderID = r.ReaderID
JOIN Books b ON l.ISBN = b.ISBN
WHERE l.ReturnDate IS NULL;


-- 2. 图书库存预警视图：显示库存数量低于3本的图书。
CREATE VIEW LowStockBooks AS
SELECT 
    b.Title AS BookTitle, 
    b.ISBN, 
    b.Quantity, 
    b.AvailableQuantity
FROM Books b
WHERE b.AvailableQuantity < 3;


-- 3. 读者类型借阅权限详细视图：显示每种读者类型的详细借阅权限及当前借阅情况。
CREATE VIEW ReaderTypePermissionsDetail AS
SELECT 
    rt.ReaderType, 
    rt.MaxBooks, 
    rt.BorrowDuration, 
    rt.MaxRenewals, 
    COUNT(l.LoanID) AS CurrentBorrowings
FROM ReadersType rt
LEFT JOIN Readers r ON rt.ReaderType = r.ReaderType
LEFT JOIN Loans l ON r.ReaderID = l.ReaderID AND l.ReturnDate IS NULL
GROUP BY rt.ReaderType, rt.MaxBooks, rt.BorrowDuration, rt.MaxRenewals;



-- 触发器：
-- 1. 图书借阅后，减去可借图书数量
DELIMITER //
CREATE TRIGGER trg_AfterLoanInsert
AFTER INSERT ON Loans
FOR EACH ROW
BEGIN
    -- 减少该图书的可借数量
    UPDATE Books
    SET AvailableQuantity = AvailableQuantity - 1
    WHERE ISBN = NEW.ISBN;
END//
DELIMITER ;

-- 2. 图书借阅删除，恢复可借数量
DELIMITER //
CREATE TRIGGER trg_AfterLoanDelete
AFTER DELETE ON Loans
FOR EACH ROW
BEGIN
    -- 恢复该图书的可借数量
    UPDATE Books
    SET AvailableQuantity = AvailableQuantity + 1
    WHERE ISBN = OLD.ISBN;
END//
DELIMITER ;



-- 存储过程：图书归还调用这个存储过程，用于计算罚款 更新图书数量
DELIMITER //
CREATE PROCEDURE sp_process_book_return(
    IN p_loan_id INT,
    IN p_new_return_date DATETIME
)
BEGIN
    DECLARE v_isbn VARCHAR(20);
    DECLARE v_old_return_date DATETIME;
    DECLARE v_due_date DATETIME;
    DECLARE v_fine_per_day FLOAT;
    DECLARE v_days_late INT;
    DECLARE v_fine_amount DECIMAL(10,2);
    
    -- 获取借阅记录的原始信息
    SELECT ISBN, ReturnDate, DueDate 
    INTO v_isbn, v_old_return_date, v_due_date
    FROM Loans 
    WHERE LoanID = p_loan_id;
    
    -- 检查归还日期是否发生变化
    IF (v_old_return_date IS NULL OR p_new_return_date <> v_old_return_date) THEN
        -- 获取该图书类别的每日罚款金额
        SELECT bc.FineAmount INTO v_fine_per_day
        FROM BookCategory bc
        JOIN Books b ON b.BookCategoryID = bc.BookCategoryID
        WHERE b.ISBN = v_isbn;
        
        -- 检查是否超期
        IF p_new_return_date > v_due_date THEN
            -- 计算超期天数
            SET v_days_late = DATEDIFF(p_new_return_date, v_due_date);
            
            -- 计算罚款金额
            SET v_fine_amount = v_fine_per_day * v_days_late;
            
            -- 更新借阅记录的罚款金额
            UPDATE Loans
            SET FineAmount = v_fine_amount
            WHERE LoanID = p_loan_id;
        END IF;
        
        -- 更新图书可借数量
        UPDATE Books
        SET AvailableQuantity = AvailableQuantity + 1
        WHERE ISBN = v_isbn;
        
        -- 更新归还日期
        UPDATE Loans
        SET ReturnDate = p_new_return_date
        WHERE LoanID = p_loan_id;
        
        -- 返回处理结果
        SELECT CONCAT('图书归还处理完成，', 
                     IF(v_days_late > 0, 
                        CONCAT('超期', v_days_late, '天，罚款金额:', v_fine_amount, '元'), 
                        '未超期')) AS result;
    ELSE
        SELECT '归还日期未发生变化，无需处理' AS result;
    END IF;
END //
DELIMITER ;



-- 统计分析查询
-- 1.查询过去30天内每天借出的图书数量：
SELECT 
    DATE(LoanDate) AS LoanDate, 
    COUNT(*) AS BooksBorrowed
FROM Loans
WHERE LoanDate >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(LoanDate)
ORDER BY LoanDate;

-- 2.查询各类别图书的平均借阅时长及最长借阅时长：
SELECT 
    bc.CategoryName, 
    AVG(DATEDIFF(IFNULL(l.ReturnDate, CURRENT_DATE), l.LoanDate)) AS AvgBorrowDuration,
    MAX(DATEDIFF(IFNULL(l.ReturnDate, CURRENT_DATE), l.LoanDate)) AS MaxBorrowDuration
FROM Loans l
JOIN Books b ON l.ISBN = b.ISBN
JOIN BookCategory bc ON b.BookCategoryID = bc.BookCategoryID
GROUP BY bc.CategoryName;

-- 3.查询教师所在学院近60天内借阅量最高的5本图书：
SELECT  
    b.Title AS BookTitle, 
    COUNT(l.LoanID) AS BorrowCount
FROM Loans l
JOIN Readers r ON l.ReaderID = r.ReaderID
JOIN Books b ON l.ISBN = b.ISBN
WHERE r.Department = '计算机学院' AND l.LoanDate >= DATE_SUB(CURRENT_DATE, INTERVAL 60 DAY)
GROUP BY b.Title
ORDER BY BorrowCount DESC
LIMIT 5;

-- 4.查询学生个人在过去一年内累计借阅时长超过30天的图书列表：
SELECT 
   l.ReaderID AS StudentID,
    b.Title AS BookTitle,
    SUM(DATEDIFF(IFNULL(l.ReturnDate, CURRENT_DATE), l.LoanDate)) AS TotalBorrowDuration
FROM Loans l
JOIN Books b ON l.ISBN = b.ISBN
WHERE l.LoanDate >= DATE_SUB(CURRENT_DATE, INTERVAL 1 YEAR)
GROUP BY l.ReaderID, b.Title
HAVING SUM(DATEDIFF(IFNULL(l.ReturnDate, CURRENT_DATE), l.LoanDate)) > 30;

-- 5.查询每个读者类型的平均借阅数量及最大借阅数量：
SELECT 
    rt.ReaderType, 
    AVG(ReaderBorrowCount.BorrowCount) AS AverageBorrow,
    MAX(ReaderBorrowCount.BorrowCount) AS MaxBorrow
FROM 
    (SELECT ReaderID, COUNT(*) AS BorrowCount FROM Loans GROUP BY ReaderID) AS ReaderBorrowCount
JOIN Readers r ON ReaderBorrowCount.ReaderID = r.ReaderID
JOIN ReadersType rt ON r.ReaderType = rt.ReaderType
GROUP BY rt.ReaderType;


-- =====================================================
-- 图书借阅管理系统数据库扩展脚本
-- 用于支持三类用户：系统管理员、读者、工作人员
-- =====================================================

-- =====================================================
-- 步骤1.1：创建工作人员表 (Staff)
-- =====================================================

-- 创建工作人员表
CREATE TABLE IF NOT EXISTS Staff (
    StaffID VARCHAR(10) NOT NULL PRIMARY KEY,           -- 工作证号
    Name VARCHAR(50) NOT NULL,                          -- 姓名
    Phone VARCHAR(15),                                  -- 电话
    Department VARCHAR(100),                            -- 部门
    Position VARCHAR(50),                               -- 职位
    HireDate DATE DEFAULT (CURRENT_DATE),               -- 入职日期（默认当前日期）
    Status ENUM('active', 'inactive') DEFAULT 'active', -- 状态（活跃/非活跃）
    Pwd VARCHAR(20) NOT NULL DEFAULT '123456',          -- 密码（默认123456）
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- 更新时间
);

-- 添加工作人员表注释
ALTER TABLE Staff COMMENT = '工作人员信息表';
ALTER TABLE Staff MODIFY COLUMN StaffID VARCHAR(10) COMMENT '工作证号（主键）';
ALTER TABLE Staff MODIFY COLUMN Name VARCHAR(50) COMMENT '工作人员姓名';
ALTER TABLE Staff MODIFY COLUMN Phone VARCHAR(15) COMMENT '联系电话';
ALTER TABLE Staff MODIFY COLUMN Department VARCHAR(100) COMMENT '所属部门';
ALTER TABLE Staff MODIFY COLUMN Position VARCHAR(50) COMMENT '职位';
ALTER TABLE Staff MODIFY COLUMN HireDate DATE COMMENT '入职日期';
ALTER TABLE Staff MODIFY COLUMN Status ENUM('active', 'inactive') COMMENT '状态：active-活跃，inactive-非活跃';
ALTER TABLE Staff MODIFY COLUMN Pwd VARCHAR(20) COMMENT '登录密码';

-- =====================================================
-- 步骤1.2：扩展读者表，添加证件状态字段
-- =====================================================

-- 为读者表添加证件状态字段
ALTER TABLE Readers ADD COLUMN IF NOT EXISTS Status ENUM('valid', 'invalid') DEFAULT 'valid' COMMENT '证件状态：valid-有效，invalid-失效';

-- 为读者表添加更新时间字段（如果不存在）
ALTER TABLE Readers ADD COLUMN IF NOT EXISTS UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间';

-- =====================================================
-- 步骤1.3：完善借阅表，优化罚款计算
-- =====================================================

-- 确保借阅表有正确的罚款字段类型
ALTER TABLE Loans MODIFY COLUMN FineAmount DECIMAL(10, 2) DEFAULT 0.00 COMMENT '罚款金额';

-- 添加罚款状态字段
ALTER TABLE Loans ADD COLUMN IF NOT EXISTS FineStatus ENUM('unpaid', 'paid', 'waived') DEFAULT 'unpaid' COMMENT '罚款状态：unpaid-未付，paid-已付，waived-免除';

-- 添加处理工作人员字段（记录是谁处理的借阅/归还）
ALTER TABLE Loans ADD COLUMN IF NOT EXISTS ProcessedBy VARCHAR(10) COMMENT '处理人员（工作人员ID或admin）';

-- 添加处理时间字段
ALTER TABLE Loans ADD COLUMN IF NOT EXISTS ProcessedAt TIMESTAMP NULL COMMENT '处理时间';

-- 添加备注字段
ALTER TABLE Loans ADD COLUMN IF NOT EXISTS Remarks TEXT COMMENT '备注信息';

-- =====================================================
-- 插入测试数据
-- =====================================================

-- 插入测试工作人员数据
INSERT IGNORE INTO Staff (StaffID, Name, Phone, Department, Position, HireDate, Status, Pwd) VALUES
('S001', '张图书', '13800138001', '借阅部', '图书管理员', '2023-01-15', 'active', '123456'),
('S002', '李管理', '13800138002', '借阅部', '高级管理员', '2022-06-01', 'active', '123456'),
('S003', '王助理', '13800138003', '技术部', '系统助理', '2023-03-20', 'active', '123456'),
('S004', '赵老师', '13800138004', '借阅部', '图书管理员', '2021-09-01', 'inactive', '123456');

-- 更新现有读者的证件状态（设置一些为失效状态用于测试）
UPDATE Readers SET Status = 'valid' WHERE ReaderID IN ('2021001', '2021002', '2021003');
UPDATE Readers SET Status = 'invalid' WHERE ReaderID = '2021004';

-- =====================================================
-- 创建视图和索引优化
-- =====================================================

-- 创建活跃工作人员视图
CREATE OR REPLACE VIEW ActiveStaff AS
SELECT
    StaffID,
    Name,
    Phone,
    Department,
    Position,
    HireDate,
    DATEDIFF(CURRENT_DATE, HireDate) AS WorkDays
FROM Staff
WHERE Status = 'active'
ORDER BY HireDate;

-- 创建有效读者视图
CREATE OR REPLACE VIEW ValidReaders AS
SELECT
    r.ReaderID,
    r.Name,
    r.Department,
    r.Phone,
    r.Email,
    r.ReaderType,
    r.RegistrationDate,
    rt.MaxBooks,
    rt.BorrowDuration
FROM Readers r
JOIN ReadersType rt ON r.ReaderType = rt.ReaderType
WHERE r.Status = 'valid'
ORDER BY r.RegistrationDate DESC;

-- 创建逾期借阅详情视图
CREATE OR REPLACE VIEW OverdueLoansDetail AS
SELECT
    l.LoanID,
    l.ReaderID,
    r.Name AS ReaderName,
    l.ISBN,
    b.Title AS BookTitle,
    l.LoanDate,
    l.DueDate,
    DATEDIFF(CURRENT_DATE, l.DueDate) AS OverdueDays,
    CASE
        WHEN DATEDIFF(CURRENT_DATE, l.DueDate) > 0
        THEN DATEDIFF(CURRENT_DATE, l.DueDate) * 0.5
        ELSE 0
    END AS CalculatedFine,
    l.FineAmount,
    l.FineStatus,
    l.ProcessedBy
FROM Loans l
JOIN Readers r ON l.ReaderID = r.ReaderID
JOIN Books b ON l.ISBN = b.ISBN
WHERE l.ReturnDate IS NULL
  AND l.DueDate < CURRENT_DATE
ORDER BY l.DueDate;

-- =====================================================
-- 创建索引优化查询性能
-- =====================================================

-- 为工作人员表创建索引
CREATE INDEX IF NOT EXISTS idx_staff_status ON Staff(Status);
CREATE INDEX IF NOT EXISTS idx_staff_department ON Staff(Department);

-- 为读者表状态字段创建索引
CREATE INDEX IF NOT EXISTS idx_readers_status ON Readers(Status);

-- 为借阅表罚款相关字段创建索引
CREATE INDEX IF NOT EXISTS idx_loans_fine_status ON Loans(FineStatus);
CREATE INDEX IF NOT EXISTS idx_loans_processed_by ON Loans(ProcessedBy);
CREATE INDEX IF NOT EXISTS idx_loans_due_date ON Loans(DueDate);

-- =====================================================
-- 创建触发器：自动计算罚款
-- =====================================================

DELIMITER //

-- 创建触发器：在归还图书时自动计算罚款
CREATE TRIGGER IF NOT EXISTS trg_CalculateFineOnReturn
BEFORE UPDATE ON Loans
FOR EACH ROW
BEGIN
    -- 只有在设置归还日期时才计算罚款
    IF NEW.ReturnDate IS NOT NULL AND OLD.ReturnDate IS NULL THEN
        -- 计算逾期天数
        SET @overdue_days = DATEDIFF(NEW.ReturnDate, OLD.DueDate);

        -- 如果逾期，计算罚款（每天0.5元）
        IF @overdue_days > 0 THEN
            SET NEW.FineAmount = @overdue_days * 0.5;
            SET NEW.FineStatus = 'unpaid';
        ELSE
            SET NEW.FineAmount = 0.00;
            SET NEW.FineStatus = 'paid';
        END IF;

        -- 设置处理时间
        SET NEW.ProcessedAt = CURRENT_TIMESTAMP;
    END IF;
END//

DELIMITER ;

-- =====================================================
-- 数据完整性检查
-- =====================================================

-- 检查工作人员表是否创建成功
SELECT 'Staff table created successfully' AS Status, COUNT(*) AS RecordCount FROM Staff;

-- 检查读者表状态字段是否添加成功
SELECT 'Reader status field added' AS Status,
       COUNT(*) AS TotalReaders,
       SUM(CASE WHEN Status = 'valid' THEN 1 ELSE 0 END) AS ValidReaders,
       SUM(CASE WHEN Status = 'invalid' THEN 1 ELSE 0 END) AS InvalidReaders
FROM Readers;

-- 检查借阅表字段是否添加成功
SELECT 'Loan table enhanced' AS Status, COUNT(*) AS TotalLoans FROM Loans;

-- 显示所有视图
SHOW TABLES LIKE '%Staff%';
SHOW TABLES LIKE '%Valid%';
SHOW TABLES LIKE '%Overdue%';

COMMIT;

-- 脚本执行完成提示
SELECT '数据库扩展脚本执行完成！' AS Message,
       '工作人员表已创建，读者表已扩展，借阅表已完善' AS Details,
       NOW() AS CompletedAt;


