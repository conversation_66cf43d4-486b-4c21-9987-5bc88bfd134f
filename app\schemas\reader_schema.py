#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
读者相关数据验证和序列化模式
"""

from marshmallow import Schema, fields, validate, validates, ValidationError

from .. import ma
from ..models.reader import Reader, ReaderType


class ReaderTypeSchema(ma.SQLAlchemyAutoSchema):
    """读者类型模式"""
    class Meta:
        model = ReaderType
        load_instance = True
    
    reader_type = fields.String(required=True, validate=validate.Length(min=1, max=20))
    max_books = fields.Integer(required=True, validate=validate.Range(min=1))
    borrow_duration = fields.Integer(required=True, validate=validate.Range(min=1))
    max_renewals = fields.Integer(required=True, validate=validate.Range(min=0))


class ReaderSchema(ma.SQLAlchemyAutoSchema):
    """读者模式"""
    class Meta:
        model = Reader
        load_instance = True
        include_fk = True
    
    reader_id = fields.String(required=True, validate=validate.Length(min=1, max=10))
    name = fields.String(required=True, validate=validate.Length(min=1, max=50))
    department = fields.String(validate=validate.Length(max=100))
    phone = fields.String(validate=validate.Length(max=15))
    email = fields.Email(validate=validate.Length(max=100))
    reader_type = fields.String(required=True, validate=validate.Length(min=1, max=20))
    registration_date = fields.Date()
    pwd = fields.String(validate=validate.Length(min=1, max=20))
    
    @validates('reader_type')
    def validate_reader_type(self, value):
        """验证读者类型是否存在"""
        reader_type = ReaderType.query.get(value)
        if not reader_type:
            raise ValidationError(f'读者类型 {value} 不存在')


class ReaderLoginSchema(Schema):
    """读者登录模式"""
    reader_id = fields.String(required=True, validate=validate.Length(min=1, max=10))
    password = fields.String(required=True, validate=validate.Length(min=1, max=20))
    remember = fields.Boolean()


class ReaderPasswordChangeSchema(Schema):
    """读者密码修改模式"""
    old_password = fields.String(required=True, validate=validate.Length(min=1, max=20))
    new_password = fields.String(required=True, validate=validate.Length(min=1, max=20))
    confirm_password = fields.String(required=True, validate=validate.Length(min=1, max=20))
    
    @validates('confirm_password')
    def validate_confirm_password(self, value, **kwargs):
        """验证确认密码是否与新密码一致"""
        if value != kwargs['data']['new_password']:
            raise ValidationError('确认密码与新密码不一致')
