{% extends 'base.html' %}

{% block title %}图书管理 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid px-3 py-2 dashboard-optimized">
    <!-- 搜索卡片 -->
    <div class="chart-card mb-3 zoom-in">
        <div class="card-header">
            <h6 class="card-title">
                <i class="bi bi-search"></i>
                图书搜索
            </h6>
            <div class="card-actions">
                <a href="{{ url_for('admin.create_book') }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle"></i> 添加图书
                </a>
            </div>
        </div>
        <div class="card-body">
            <form action="{{ url_for('admin.books') }}" method="get" class="search-form">
                <div class="row g-3">
                    <div class="col-md-7">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-search text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 ps-0"
                                placeholder="输入ISBN、书名、作者或出版社..."
                                name="q" value="{{ search_query }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="category">
                            <option value="">所有类别</option>
                            {% for category in categories %}
                            <option value="{{ category.book_category_id }}" {% if category_id == category.book_category_id %}selected{% endif %}>
                                {{ category.category_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 图书列表 -->
    <div class="chart-card mb-3 fade-in">
        <div class="card-header">
            <h6 class="card-title">
                <i class="bi bi-book"></i>
                图书列表
            </h6>
            <div class="card-actions">
                <span class="badge bg-primary rounded-pill">
                    共 {{ pagination.total }} 本图书
                </span>
            </div>
        </div>
        <div class="card-body p-0">
            {% if books %}
            <div class="table-responsive">
                <table class="table table-admin table-hover mb-0" id="booksTable">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="isbn">
                                ISBN <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="title">
                                书名 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="author">
                                作者 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="publisher">
                                出版社 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="year">
                                出版年份 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="category">
                                类别 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="quantity">
                                总数量 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th class="sortable" data-sort="available">
                                可借数量 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                            </th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for book in books %}
                        <tr class="book-row">
                            <td>
                                <span class="text-monospace">{{ book.isbn }}</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-xs me-2 bg-primary text-white rounded-circle">
                                        <i class="bi bi-book"></i>
                                    </div>
                                    <span>{{ book.title }}</span>
                                </div>
                            </td>
                            <td>{{ book.author }}</td>
                            <td>{{ book.publisher }}</td>
                            <td>{{ book.year }}</td>
                            <td>
                                <span class="status-badge badge-primary">
                                    {{ book.category.category_name }}
                                </span>
                            </td>
                            <td>{{ book.quantity }}</td>
                            <td>
                                {% if book.available_quantity > 0 %}
                                <span class="badge bg-success">{{ book.available_quantity }}</span>
                                {% else %}
                                <span class="badge bg-danger">0</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('admin.book_detail', isbn=book.isbn) }}"
                                       class="btn btn-sm btn-outline-info"
                                       data-bs-toggle="tooltip"
                                       title="查看详情">
                                        <i class="bi bi-info-circle"></i>
                                    </a>
                                    <a href="{{ url_for('admin.edit_book', isbn=book.isbn) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       data-bs-toggle="tooltip"
                                       title="编辑图书">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <div class="p-3 d-flex justify-content-between align-items-center border-top">
                <div class="pagination-info">
                    显示 {{ pagination.items|length }} 条，共 {{ pagination.total }} 条
                </div>
                <ul class="pagination mb-0">
                    {% for page in pagination.iter_pages(left_edge=2, left_current=2, right_current=3, right_edge=2) %}
                    {% if page %}
                    <li class="page-item {% if page == pagination.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('admin.books', page=page, q=search_query, category=category_id) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            {% else %}
            <div class="empty-state p-5 text-center">
                <i class="bi bi-book text-muted mb-3"></i>
                <p class="mb-0">暂无图书数据</p>
                <p class="text-muted small">请添加图书或修改搜索条件</p>
                <a href="{{ url_for('admin.create_book') }}" class="btn btn-primary mt-3">
                    <i class="bi bi-plus-circle"></i> 添加图书
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表格排序功能
        const sortableHeaders = document.querySelectorAll('th.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const sortBy = this.dataset.sort;
                const currentOrder = this.classList.contains('sort-asc') ? 'desc' : 'asc';
                
                // 清除所有排序状态
                sortableHeaders.forEach(h => {
                    h.classList.remove('sort-asc', 'sort-desc');
                });
                
                // 设置当前排序状态
                this.classList.add(`sort-${currentOrder}`);
                
                // 排序表格
                sortTable('booksTable', sortBy, currentOrder);
            });
        });
        
        // 初始化工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
    
    // 表格排序函数
    function sortTable(tableId, sortBy, order) {
        const table = document.getElementById(tableId);
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        // 根据不同的列进行排序
        rows.sort((a, b) => {
            let aValue, bValue;
            
            switch(sortBy) {
                case 'isbn':
                    aValue = a.cells[0].textContent.trim();
                    bValue = b.cells[0].textContent.trim();
                    break;
                case 'title':
                    aValue = a.cells[1].textContent.trim();
                    bValue = b.cells[1].textContent.trim();
                    break;
                case 'author':
                    aValue = a.cells[2].textContent.trim();
                    bValue = b.cells[2].textContent.trim();
                    break;
                case 'publisher':
                    aValue = a.cells[3].textContent.trim();
                    bValue = b.cells[3].textContent.trim();
                    break;
                case 'year':
                    aValue = parseInt(a.cells[4].textContent.trim());
                    bValue = parseInt(b.cells[4].textContent.trim());
                    break;
                case 'category':
                    aValue = a.cells[5].textContent.trim();
                    bValue = b.cells[5].textContent.trim();
                    break;
                case 'quantity':
                    aValue = parseInt(a.cells[6].textContent.trim());
                    bValue = parseInt(b.cells[6].textContent.trim());
                    break;
                case 'available':
                    aValue = parseInt(a.cells[7].textContent.trim());
                    bValue = parseInt(b.cells[7].textContent.trim());
                    break;
                default:
                    return 0;
            }
            
            // 比较值
            if (aValue < bValue) {
                return order === 'asc' ? -1 : 1;
            }
            if (aValue > bValue) {
                return order === 'asc' ? 1 : -1;
            }
            return 0;
        });
        
        // 重新排列表格行
        rows.forEach(row => tbody.appendChild(row));
    }
</script>
{% endblock %}
