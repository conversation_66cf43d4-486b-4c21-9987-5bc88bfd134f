{% extends 'base.html' %}

{% block title %}归还确认 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/reader.css') }}">
<style>
    .return-card {
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        background: linear-gradient(to right, #f8f9fa, #ffffff);
        border: none;
    }
    
    .return-header {
        background: linear-gradient(135deg, #1a3a5f, #4299e1);
        color: white;
        padding: 1.5rem;
    }
    
    .return-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .return-subtitle {
        opacity: 0.8;
    }
    
    .book-info {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .book-icon {
        width: 60px;
        height: 60px;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        background: linear-gradient(135deg, #1a3a5f, #4299e1);
        margin-right: 1rem;
    }
    
    .book-title {
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 0.25rem;
    }
    
    .book-author {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .loan-details {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .detail-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }
    
    .detail-label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .detail-value {
        font-weight: 600;
    }
    
    .fine-alert {
        background-color: rgba(255, 193, 7, 0.1);
        border-left: 4px solid #ffc107;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    .fine-title {
        color: #856404;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }
    
    .fine-title i {
        margin-right: 0.5rem;
    }
    
    .fine-text {
        color: #6c757d;
        margin-bottom: 0;
    }
    
    .fine-amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: #dc3545;
        text-align: center;
        margin: 1rem 0;
    }
    
    .action-buttons {
        display: flex;
        justify-content: space-between;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="return-card">
                <div class="return-header">
                    <h4 class="return-title">归还确认</h4>
                    <p class="return-subtitle mb-0">请确认以下归还信息</p>
                </div>
                <div class="card-body p-4">
                    <!-- 图书信息 -->
                    <div class="book-info">
                        <div class="book-icon">
                            <i class="bi bi-book"></i>
                        </div>
                        <div>
                            <h5 class="book-title">{{ loan.book.title }}</h5>
                            <p class="book-author">{{ loan.book.author }}</p>
                        </div>
                    </div>
                    
                    <!-- 借阅详情 -->
                    <div class="loan-details">
                        <div class="detail-item">
                            <span class="detail-label">借阅日期</span>
                            <span class="detail-value">{{ loan.loan_date|datetime('%Y-%m-%d') }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">应还日期</span>
                            <span class="detail-value">{{ loan.due_date|datetime('%Y-%m-%d') }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">当前日期</span>
                            <span class="detail-value">{{ now|datetime('%Y-%m-%d') }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">借阅状态</span>
                            <span class="detail-value">
                                {% if loan.is_overdue() %}
                                <span class="badge bg-danger">已逾期 {{ loan.get_overdue_days() }} 天</span>
                                {% else %}
                                <span class="badge bg-success">正常</span>
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    
                    <!-- 罚款信息 -->
                    {% if loan.is_overdue() %}
                    <div class="fine-alert">
                        <h6 class="fine-title">
                            <i class="bi bi-exclamation-triangle-fill"></i>
                            逾期罚款
                        </h6>
                        <p class="fine-text">此图书已逾期 {{ loan.get_overdue_days() }} 天，根据图书类别 "{{ loan.book.category.category_name }}" 的规定，每天罚款 {{ loan.book.category.fine_amount }} 元。</p>
                        <div class="fine-amount">
                            罚款金额：{{ loan.calculate_fine() }} 元
                        </div>
                        <p class="fine-text text-center">请在归还图书后到图书馆前台缴纳罚款。</p>
                    </div>
                    {% endif %}
                    
                    <!-- 操作按钮 -->
                    <div class="action-buttons">
                        <a href="{{ url_for('reader.loans') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回借阅记录
                        </a>
                        <form action="{{ url_for('reader.confirm_return', loan_id=loan.loan_id) }}" method="post">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-warning">
                                <i class="bi bi-journal-arrow-up"></i> 确认归还
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
