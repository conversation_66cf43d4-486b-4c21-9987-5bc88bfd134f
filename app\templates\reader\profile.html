{% extends 'base.html' %}

{% block title %}个人信息 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/reader.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8">
            <!-- 个人信息卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">个人信息</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 30%">读者编号</th>
                                    <td>{{ current_user.reader_id }}</td>
                                </tr>
                                <tr>
                                    <th>姓名</th>
                                    <td>{{ current_user.name }}</td>
                                </tr>
                                <tr>
                                    <th>读者类型</th>
                                    <td>{{ current_user.reader_type }}</td>
                                </tr>
                                <tr>
                                    <th>学院/部门</th>
                                    <td>{{ current_user.department }}</td>
                                </tr>
                                <tr>
                                    <th>手机号码</th>
                                    <td>{{ current_user.phone }}</td>
                                </tr>
                                <tr>
                                    <th>电子邮箱</th>
                                    <td>{{ current_user.email }}</td>
                                </tr>
                                <tr>
                                    <th>注册日期</th>
                                    <td>{{ current_user.registration_date|datetime('%Y-%m-%d') }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-primary">
                            <i class="bi bi-key"></i> 修改密码
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 借阅权限卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">借阅权限</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5 class="small font-weight-bold">最大可借数量 <span class="float-right">{{ current_user.reader_type_info.max_books }}</span></h5>
                        <div class="progress mb-4">
                            {% set percent = (current_user.get_current_loans()|length / current_user.reader_type_info.max_books * 100)|int %}
                            <div class="progress-bar bg-info" role="progressbar" style="width: {{ percent }}%" aria-valuenow="{{ percent }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h5 class="small font-weight-bold">借阅期限 <span class="float-right">{{ current_user.reader_type_info.borrow_duration }} 天</span></h5>
                    </div>
                    <div class="mb-4">
                        <h5 class="small font-weight-bold">最大续借次数 <span class="float-right">{{ current_user.reader_type_info.max_renewals }} 次</span></h5>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('reader.loans') }}" class="btn btn-info btn-sm">
                            <i class="bi bi-journal-text"></i> 查看借阅记录
                        </a>
                    </div>
                </div>
            </div>

            <!-- 借阅统计卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">借阅统计</h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <i class="bi bi-journal-text fa-2x text-gray-300"></i>
                        </div>
                        <div class="col">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_user.get_current_loans()|length }}</div>
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">当前借阅</div>
                        </div>
                    </div>
                    <hr>
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <i class="bi bi-journal-check fa-2x text-gray-300"></i>
                        </div>
                        <div class="col">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_user.get_loan_history()|length }}</div>
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">历史借阅</div>
                        </div>
                    </div>
                    <hr>
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                        <div class="col">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_user.get_overdue_loans()|length }}</div>
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">逾期借阅</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
