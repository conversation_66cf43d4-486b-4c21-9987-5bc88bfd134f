#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
认证视图
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user

from .. import db
from ..models.reader import Reader, admin_instance

# 创建蓝图
auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if current_user.is_authenticated:
        # 根据用户类型重定向
        user_id = current_user.get_id()
        if user_id.startswith('admin_'):
            return redirect(url_for('admin.dashboard'))
        elif user_id.startswith('staff_'):
            return redirect(url_for('staff.dashboard'))
        else:
            return redirect(url_for('reader.dashboard'))

    if request.method == 'POST':
        user_type = request.form.get('user_type')
        username = request.form.get('username')
        password = request.form.get('password')
        remember = request.form.get('remember') == 'on'

        if not username or not password:
            flash('请输入用户名和密码', 'warning')
            return render_template('auth/login.html')

        if user_type == 'admin':
            # 管理员登录（使用硬编码的管理员账户）
            if username == 'admin' and password == '1':
                login_user(admin_instance, remember=remember)
                flash('登录成功', 'success')
                next_page = request.args.get('next')
                return redirect(next_page or url_for('admin.dashboard'))
            else:
                flash('用户名或密码错误', 'danger')
        elif user_type == 'staff':
            # 工作人员登录
            from ..models.staff import Staff
            staff = Staff.authenticate(username, password)
            if staff:
                login_user(staff, remember=remember)
                flash('登录成功', 'success')
                next_page = request.args.get('next')
                return redirect(next_page or url_for('staff.dashboard'))
            else:
                flash('用户名或密码错误，或账户已被停用', 'danger')
        else:
            # 读者登录
            reader = Reader.query.get(username)
            if reader and reader.verify_password(password):
                # 检查证件状态
                if not reader.is_valid():
                    flash('您的证件已失效，无法登录系统', 'danger')
                else:
                    login_user(reader, remember=remember)
                    flash('登录成功', 'success')
                    next_page = request.args.get('next')
                    return redirect(next_page or url_for('reader.dashboard'))
            else:
                flash('用户名或密码错误', 'danger')

    return render_template('auth/login.html')


@auth_bp.route('/logout')
@login_required
def logout():
    """登出"""
    logout_user()
    flash('您已成功登出', 'success')
    return redirect(url_for('auth.login'))


@auth_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    if request.method == 'POST':
        old_password = request.form.get('old_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if not old_password or not new_password or not confirm_password:
            flash('请填写所有字段', 'warning')
            return render_template('auth/change_password.html')

        if new_password != confirm_password:
            flash('新密码和确认密码不一致', 'danger')
            return render_template('auth/change_password.html')

        if not current_user.verify_password(old_password):
            flash('原密码错误', 'danger')
            return render_template('auth/change_password.html')

        # 更新密码
        user_id = current_user.get_id()
        if user_id.startswith('admin_'):
            # 管理员 - 不允许修改硬编码的管理员密码
            flash('管理员密码不允许修改', 'warning')
            return redirect(url_for('admin.dashboard'))
        elif user_id.startswith('staff_'):
            # 工作人员
            current_user.change_password(new_password)
            flash('密码修改成功', 'success')
            return redirect(url_for('staff.dashboard'))
        else:
            # 读者
            current_user.pwd = new_password
            db.session.commit()
            flash('密码修改成功', 'success')
            return redirect(url_for('reader.dashboard'))

    return render_template('auth/change_password.html')
