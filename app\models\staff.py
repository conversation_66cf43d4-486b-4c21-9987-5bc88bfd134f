#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工作人员相关模型
"""

from datetime import datetime
import pytz
from flask_login import UserMixin

from .. import db


class Staff(db.Model, UserMixin):
    """工作人员模型"""
    __tablename__ = 'Staff'

    staff_id = db.Column('StaffID', db.String(10), primary_key=True)
    name = db.Column('Name', db.String(50), nullable=False)
    phone = db.Column('Phone', db.String(15))
    department = db.Column('Department', db.String(100))
    position = db.Column('Position', db.String(50))
    hire_date = db.Column('HireDate', db.Date, default=datetime.now(pytz.timezone('Asia/Shanghai')).date)
    status = db.Column('Status', db.Enum('active', 'inactive'), default='active')
    pwd = db.Column('Pwd', db.String(20), default='123456')
    created_at = db.Column('CreatedAt', db.DateTime, default=datetime.now(pytz.timezone('Asia/Shanghai')))
    updated_at = db.Column('UpdatedAt', db.DateTime, 
                          default=datetime.now(pytz.timezone('Asia/Shanghai')),
                          onupdate=datetime.now(pytz.timezone('Asia/Shanghai')))

    def __repr__(self):
        return f'<Staff {self.staff_id}>'

    def get_id(self):
        """获取用户ID，用于Flask-Login"""
        return f"staff_{self.staff_id}"

    def verify_password(self, password):
        """验证密码"""
        return self.pwd == password

    def is_active_staff(self):
        """检查是否为活跃工作人员"""
        return self.status == 'active'

    def get_work_days(self):
        """获取工作天数"""
        if self.hire_date:
            today = datetime.now(pytz.timezone('Asia/Shanghai')).date()
            return (today - self.hire_date).days
        return 0

    def get_processed_loans_count(self):
        """获取处理的借阅记录数量"""
        from .loan import Loan
        return Loan.query.filter_by(processed_by=self.staff_id).count()

    def get_recent_processed_loans(self, limit=10):
        """获取最近处理的借阅记录"""
        from .loan import Loan
        return Loan.query.filter_by(processed_by=self.staff_id)\
                         .order_by(Loan.processed_at.desc())\
                         .limit(limit).all()

    def can_process_loans(self):
        """检查是否可以处理借阅业务"""
        return self.is_active_staff()

    def get_display_name(self):
        """获取显示名称"""
        return f"{self.name}({self.staff_id})"

    def to_dict(self):
        """转换为字典格式"""
        return {
            'staff_id': self.staff_id,
            'name': self.name,
            'phone': self.phone,
            'department': self.department,
            'position': self.position,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'status': self.status,
            'work_days': self.get_work_days(),
            'processed_loans_count': self.get_processed_loans_count()
        }

    @staticmethod
    def get_active_staff():
        """获取所有活跃工作人员"""
        return Staff.query.filter_by(status='active').order_by(Staff.hire_date.desc()).all()

    @staticmethod
    def get_staff_by_department(department):
        """根据部门获取工作人员"""
        return Staff.query.filter_by(department=department, status='active').all()

    @staticmethod
    def create_staff(staff_id, name, phone=None, department=None, position=None, password='123456'):
        """创建新工作人员"""
        staff = Staff(
            staff_id=staff_id,
            name=name,
            phone=phone,
            department=department,
            position=position,
            pwd=password,
            status='active'
        )
        db.session.add(staff)
        db.session.commit()
        return staff

    @staticmethod
    def authenticate(staff_id, password):
        """工作人员认证"""
        staff = Staff.query.get(staff_id)
        if staff and staff.verify_password(password) and staff.is_active_staff():
            return staff
        return None

    def update_info(self, **kwargs):
        """更新工作人员信息"""
        allowed_fields = ['name', 'phone', 'department', 'position', 'status']
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(self, field):
                setattr(self, field, value)
        
        self.updated_at = datetime.now(pytz.timezone('Asia/Shanghai'))
        db.session.commit()

    def change_password(self, new_password):
        """修改密码"""
        self.pwd = new_password
        self.updated_at = datetime.now(pytz.timezone('Asia/Shanghai'))
        db.session.commit()

    def deactivate(self):
        """停用工作人员账户"""
        self.status = 'inactive'
        self.updated_at = datetime.now(pytz.timezone('Asia/Shanghai'))
        db.session.commit()

    def activate(self):
        """激活工作人员账户"""
        self.status = 'active'
        self.updated_at = datetime.now(pytz.timezone('Asia/Shanghai'))
        db.session.commit()

    def get_monthly_stats(self, year=None, month=None):
        """获取月度工作统计"""
        from .loan import Loan
        from sqlalchemy import extract, func
        
        if not year:
            year = datetime.now().year
        if not month:
            month = datetime.now().month
            
        # 统计当月处理的借阅和归还
        loans_processed = db.session.query(func.count(Loan.loan_id))\
                                   .filter(Loan.processed_by == self.staff_id)\
                                   .filter(extract('year', Loan.processed_at) == year)\
                                   .filter(extract('month', Loan.processed_at) == month)\
                                   .scalar() or 0
        
        return {
            'year': year,
            'month': month,
            'loans_processed': loans_processed,
            'staff_name': self.name
        }


# 工作人员权限常量
class StaffPermissions:
    """工作人员权限定义"""
    PROCESS_LOANS = 'process_loans'        # 处理借阅
    PROCESS_RETURNS = 'process_returns'    # 处理归还
    MANAGE_FINES = 'manage_fines'          # 管理罚款
    VIEW_REPORTS = 'view_reports'          # 查看报表
    
    # 默认权限集合
    DEFAULT_PERMISSIONS = [
        PROCESS_LOANS,
        PROCESS_RETURNS,
        MANAGE_FINES,
        VIEW_REPORTS
    ]

    @staticmethod
    def get_all_permissions():
        """获取所有权限"""
        return StaffPermissions.DEFAULT_PERMISSIONS

    @staticmethod
    def check_permission(staff, permission):
        """检查工作人员权限"""
        if not staff or not staff.is_active_staff():
            return False
        
        # 目前所有活跃工作人员都有默认权限
        return permission in StaffPermissions.DEFAULT_PERMISSIONS


# 工作人员统计工具类
class StaffStats:
    """工作人员统计工具"""
    
    @staticmethod
    def get_department_stats():
        """获取部门统计"""
        from sqlalchemy import func
        
        stats = db.session.query(
            Staff.department,
            func.count(Staff.staff_id).label('total_count'),
            func.sum(db.case([(Staff.status == 'active', 1)], else_=0)).label('active_count')
        ).group_by(Staff.department).all()
        
        return [
            {
                'department': stat.department or '未分配',
                'total_count': stat.total_count,
                'active_count': stat.active_count or 0
            }
            for stat in stats
        ]

    @staticmethod
    def get_top_performers(limit=5):
        """获取业绩排行榜"""
        from .loan import Loan
        from sqlalchemy import func
        
        performers = db.session.query(
            Staff.staff_id,
            Staff.name,
            Staff.department,
            func.count(Loan.loan_id).label('processed_count')
        ).join(Loan, Staff.staff_id == Loan.processed_by)\
         .filter(Staff.status == 'active')\
         .group_by(Staff.staff_id, Staff.name, Staff.department)\
         .order_by(func.count(Loan.loan_id).desc())\
         .limit(limit).all()
        
        return [
            {
                'staff_id': p.staff_id,
                'name': p.name,
                'department': p.department,
                'processed_count': p.processed_count
            }
            for p in performers
        ]

    @staticmethod
    def get_workload_distribution():
        """获取工作量分布"""
        from .loan import Loan
        from sqlalchemy import func
        
        workload = db.session.query(
            Staff.name,
            func.count(Loan.loan_id).label('loan_count')
        ).outerjoin(Loan, Staff.staff_id == Loan.processed_by)\
         .filter(Staff.status == 'active')\
         .group_by(Staff.staff_id, Staff.name)\
         .order_by(func.count(Loan.loan_id).desc()).all()
        
        return [
            {
                'name': w.name,
                'loan_count': w.loan_count or 0
            }
            for w in workload
        ]
