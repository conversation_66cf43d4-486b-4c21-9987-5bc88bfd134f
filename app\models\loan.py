#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
借阅相关模型
"""

from datetime import datetime, timedelta
import pytz

from .. import db
from ..utils.time_util import ensure_timezone


class Loan(db.Model):
    """借阅模型"""
    __tablename__ = 'Loans'

    loan_id = db.Column('LoanID', db.Integer, primary_key=True, autoincrement=True)
    reader_id = db.<PERSON>umn('ReaderID', db.String(10), db.<PERSON>('Readers.ReaderID'), nullable=False)
    isbn = db.Column('ISBN', db.String(20), db.<PERSON><PERSON><PERSON>('Books.ISBN'), nullable=False)
    loan_date = db.Column('LoanDate', db.DateTime, nullable=False, default=datetime.now(pytz.timezone('Asia/Shanghai')))
    due_date = db.Column('DueDate', db.DateTime, nullable=False)
    return_date = db.Column('ReturnDate', db.DateTime)
    fine_amount = db.Column('FineAmount', db.DECIMAL(10, 2), default=0.00)
    fine_status = db.Column('FineStatus', db.Enum('unpaid', 'paid', 'waived'), default='unpaid')
    processed_by = db.Column('ProcessedBy', db.String(10))
    processed_at = db.Column('ProcessedAt', db.DateTime)

    def __repr__(self):
        return f'<Loan {self.loan_id}>'

    def is_overdue(self):
        """检查是否逾期"""
        if self.return_date:
            return False  # 已归还

        now = datetime.now(pytz.timezone('Asia/Shanghai'))
        # 确保时区一致
        if not self.due_date.tzinfo:
            due_date = pytz.timezone('Asia/Shanghai').localize(self.due_date)
        else:
            due_date = self.due_date

        return due_date < now

    def get_overdue_days(self):
        """获取逾期天数"""
        if not self.is_overdue():
            return 0

        now = datetime.now(pytz.timezone('Asia/Shanghai'))
        # 确保时区一致
        if not self.due_date.tzinfo:
            due_date = pytz.timezone('Asia/Shanghai').localize(self.due_date)
        else:
            due_date = self.due_date

        return (now - due_date).days

    def calculate_fine(self, return_date=None):
        """计算罚款金额 - 统一使用每天0.5元的标准"""
        if not return_date:
            return_date = datetime.now(pytz.timezone('Asia/Shanghai'))

        # 确保时区一致
        if not self.due_date.tzinfo:
            due_date = pytz.timezone('Asia/Shanghai').localize(self.due_date)
        else:
            due_date = self.due_date

        if not return_date.tzinfo:
            return_date = pytz.timezone('Asia/Shanghai').localize(return_date)

        # 计算逾期天数
        if return_date > due_date:
            overdue_days = (return_date - due_date).days
            # 每天罚款0.5元（统一标准）
            return round(overdue_days * 0.5, 2)

        return 0.00

    def calculate_current_fine(self):
        """计算当前罚款金额（不需要参数的版本）"""
        if not self.is_overdue():
            return 0.00

        overdue_days = self.get_overdue_days()
        # 每天罚款0.5元（统一标准）
        return round(overdue_days * 0.5, 2)

    def process_return(self, processed_by, return_date=None):
        """处理图书归还"""
        if not return_date:
            return_date = datetime.now(pytz.timezone('Asia/Shanghai'))

        # 设置归还日期
        self.return_date = return_date

        # 计算罚款
        fine_amount = self.calculate_fine(return_date)
        self.fine_amount = fine_amount

        # 设置罚款状态
        if fine_amount > 0:
            self.fine_status = 'unpaid'
        else:
            self.fine_status = 'paid'

        # 记录处理人员和时间
        self.processed_by = processed_by
        self.processed_at = datetime.now(pytz.timezone('Asia/Shanghai'))

        # 更新图书可借数量
        if self.book:
            self.book.available_quantity += 1

        db.session.commit()
        return True

    def pay_fine(self, processed_by):
        """缴纳罚款"""
        if self.fine_amount and self.fine_amount > 0:
            self.fine_status = 'paid'
            self.processed_by = processed_by
            self.processed_at = datetime.now(pytz.timezone('Asia/Shanghai'))
            db.session.commit()
            return True
        return False

    def waive_fine(self, processed_by, reason=""):
        """免除罚款"""
        if self.fine_amount and self.fine_amount > 0:
            self.fine_status = 'waived'
            self.processed_by = processed_by
            self.processed_at = datetime.now(pytz.timezone('Asia/Shanghai'))
            if reason:
                self.remarks = reason
            db.session.commit()
            return True
        return False

    def get_status_display(self):
        """获取状态显示"""
        if self.return_date:
            return "已归还"
        elif self.is_overdue():
            return f"逾期{self.get_overdue_days()}天"
        else:
            return "借阅中"

    def get_fine_status_display(self):
        """获取罚款状态显示"""
        status_map = {
            'unpaid': '未缴纳',
            'paid': '已缴纳',
            'waived': '已免除'
        }
        return status_map.get(self.fine_status, '未知')

    @staticmethod
    def create_loan(reader_id, isbn, processed_by, borrow_duration=30):
        """创建借阅记录"""
        from .book import Book
        from .reader import Reader

        # 检查图书是否可借
        book = Book.query.get(isbn)
        if not book or book.available_quantity <= 0:
            return None, "图书不可借"

        # 检查读者是否可以借阅
        reader = Reader.query.get(reader_id)
        if not reader:
            return None, "读者不存在"

        can_borrow, message = reader.can_borrow()
        if not can_borrow:
            return None, message

        # 创建借阅记录
        loan_date = datetime.now(pytz.timezone('Asia/Shanghai'))
        due_date = loan_date + timedelta(days=borrow_duration)

        loan = Loan(
            reader_id=reader_id,
            isbn=isbn,
            loan_date=loan_date,
            due_date=due_date,
            processed_by=processed_by,
            processed_at=loan_date
        )

        # 减少图书可借数量
        book.available_quantity -= 1

        db.session.add(loan)
        db.session.commit()

        return loan, "借阅成功"

    def is_overdue(self):
        """检查是否逾期

        返回:
            bool: 是否逾期
        """
        # 确保due_date有时区信息
        due_date = ensure_timezone(self.due_date)

        if self.return_date is not None:
            # 已归还，检查归还时是否逾期
            # 确保return_date有时区信息
            return_date = ensure_timezone(self.return_date)
            return return_date > due_date
        else:
            # 未归还，检查当前时间是否超过应还日期
            now = datetime.now(pytz.timezone('Asia/Shanghai'))
            return now > due_date

    def get_overdue_days(self):
        """获取逾期天数

        返回:
            int: 逾期天数
        """
        if not self.is_overdue():
            return 0

        # 确保due_date有时区信息
        due_date = ensure_timezone(self.due_date)

        if self.return_date is not None:
            # 已归还
            # 确保return_date有时区信息
            return_date = ensure_timezone(self.return_date)
            return (return_date - due_date).days
        else:
            # 未归还
            now = datetime.now(pytz.timezone('Asia/Shanghai'))
            return (now - due_date).days



    def get_renewal_count(self):
        """计算已续借次数

        通过分析借阅记录的时间差来计算用户已续借的次数
        初始借阅期限为读者类型对应的借阅期限
        每次续借后的新到期日为当前到期日加上读者类型的借阅期限
        """
        from ..models.reader import Reader

        # 获取读者和读者类型信息
        reader = Reader.query.get(self.reader_id)
        if not reader:
            return 0

        # 获取初始借阅期限（天数）
        initial_borrow_duration = reader.reader_type_info.borrow_duration

        # 确保loan_date有时区信息
        loan_date = ensure_timezone(self.loan_date)

        # 计算理论上的初始到期日（借阅日期 + 初始借阅期限）
        initial_due_date = loan_date + timedelta(days=initial_borrow_duration)

        # 确保due_date有时区信息
        due_date = ensure_timezone(self.due_date)

        # 计算到期日之间的差距（天数）
        days_difference = (due_date - initial_due_date).days

        # 如果差距为负数或零，表示没有续借
        if days_difference <= 0:
            return 0

        # 计算续借次数（差距天数 / 借阅期限）
        renewal_count = days_difference // initial_borrow_duration

        return renewal_count

    def can_renew(self, reader):
        """检查是否可以续借"""
        # 检查是否已归还
        if self.return_date is not None:
            return False

        # 检查是否逾期
        if self.is_overdue():
            return False

        # 获取已续借次数
        renewal_count = self.get_renewal_count()

        # 检查是否超过最大续借次数
        max_renewals = reader.reader_type_info.max_renewals
        return renewal_count < max_renewals

    def renew(self, reader):
        """续借"""
        if not self.can_renew(reader):
            return False

        # 获取借阅期限
        borrow_duration = reader.reader_type_info.borrow_duration

        # 确保due_date有时区信息
        due_date = ensure_timezone(self.due_date)

        # 更新应还日期
        self.due_date = due_date + timedelta(days=borrow_duration)

        return True
