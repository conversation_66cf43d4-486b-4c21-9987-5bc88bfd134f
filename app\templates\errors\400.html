<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>400 - 请求错误</title>
    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/bootstrap.min.css') }}">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap-icons/bootstrap-icons.css') }}">
    <style>
        :root {
            --primary-dark: #1a3a5f;
            --primary-light: #4299e1;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }
        
        .error-container {
            text-align: center;
            padding: 2rem;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #fff;
        }
        
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .btn-home {
            background-color: #fff;
            color: var(--primary-dark);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-home:hover {
            background-color: var(--primary-dark);
            color: #fff;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .error-icon {
            font-size: 5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="bi bi-exclamation-circle"></i>
        </div>
        <div class="error-code">400</div>
        <div class="error-message">请求错误</div>
        <p class="mb-4">您的请求包含无效参数或格式错误。</p>
        <a href="{{ url_for('auth.login') }}" class="btn btn-home">
            <i class="bi bi-house-door"></i> 返回首页
        </a>
    </div>
    
    <!-- Bootstrap 5 JS -->
    <script src="{{ url_for('static', filename='bootstrap/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
