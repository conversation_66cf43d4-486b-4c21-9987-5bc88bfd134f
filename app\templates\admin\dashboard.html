{% extends 'base.html' %}

{% block title %}管理员仪表盘 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid px-3 py-2 dashboard-optimized">
    <!-- 统计卡片 -->
    <div class="row mb-3">
        <!-- 读者数量 -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="dashboard-card primary slide-in-up">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="card-icon primary me-3">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="text-start flex-grow-1">
                        <div class="card-title mb-1">读者数量</div>
                        <div class="dashboard-stat">{{ reader_count }}</div>
                        <div class="stat-trend text-success">
                            <i class="bi bi-graph-up-arrow"></i> 较上月增长 5%
                        </div>
                    </div>
                </div>
                <div class="card-progress">
                    <div class="progress-bar bg-primary" style="width: 75%"></div>
                </div>
            </div>
        </div>

        <!-- 图书数量 -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="dashboard-card success slide-in-up" style="animation-delay: 0.1s;">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="card-icon success me-3">
                        <i class="bi bi-book-half"></i>
                    </div>
                    <div class="text-start flex-grow-1">
                        <div class="card-title mb-1">图书数量</div>
                        <div class="dashboard-stat">{{ book_count }}</div>
                        <div class="stat-trend text-success">
                            <i class="bi bi-graph-up-arrow"></i> 较上月增长 3%
                        </div>
                    </div>
                </div>
                <div class="card-progress">
                    <div class="progress-bar bg-success" style="width: 65%"></div>
                </div>
            </div>
        </div>

        <!-- 借阅数量 -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="dashboard-card info slide-in-up" style="animation-delay: 0.2s;">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="card-icon info me-3">
                        <i class="bi bi-journal-bookmark-fill"></i>
                    </div>
                    <div class="text-start flex-grow-1">
                        <div class="card-title mb-1">借阅数量</div>
                        <div class="dashboard-stat">{{ loan_count }}</div>
                        <div class="stat-trend text-success">
                            <i class="bi bi-graph-up-arrow"></i> 较上月增长 8%
                        </div>
                    </div>
                </div>
                <div class="card-progress">
                    <div class="progress-bar bg-info" style="width: 85%"></div>
                </div>
            </div>
        </div>

        <!-- 逾期数量 -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="dashboard-card warning slide-in-up" style="animation-delay: 0.3s;">
                <div class="card-body d-flex align-items-center p-3">
                    <div class="card-icon warning me-3">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                    </div>
                    <div class="text-start flex-grow-1">
                        <div class="card-title mb-1">逾期数量</div>
                        <div class="dashboard-stat">{{ overdue_count }}</div>
                        <div class="stat-trend text-danger">
                            <i class="bi bi-graph-down-arrow"></i> 较上月减少 2%
                        </div>
                    </div>
                </div>
                <div class="card-progress">
                    <div class="progress-bar bg-warning" style="width: 35%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表和表格 -->
    <div class="row mb-3">
        <!-- 借阅趋势图表 -->
        <div class="col-xl-8 col-lg-7">
            <div class="chart-card mb-3 zoom-in">
                <div class="card-header">
                    <h6 class="card-title">
                        <i class="bi bi-graph-up-arrow"></i>
                        借阅趋势
                    </h6>
                    <div class="card-actions">
                        <div class="btn-group chart-period-selector">
                            <button class="btn btn-sm btn-primary active" data-period="week">周</button>
                            <button class="btn btn-sm btn-outline-primary" data-period="month">月</button>
                            <button class="btn btn-sm btn-outline-primary" data-period="year">年</button>
                        </div>
                    </div>
                </div>
                <div class="card-body chart-body">
                    <div class="chart-info mb-2">
                        <div class="chart-summary">
                            <div class="summary-value">{{ loan_count }}</div>
                            <div class="summary-label">总借阅量</div>
                        </div>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <span class="legend-color" style="background-color: rgba(66, 153, 225, 0.8);"></span>
                                <span class="legend-text">借阅数量</span>
                            </div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="loanTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图书类别分布 -->
        <div class="col-xl-4 col-lg-5">
            <div class="chart-card mb-3 zoom-in" style="animation-delay: 0.1s;">
                <div class="card-header">
                    <h6 class="card-title">
                        <i class="bi bi-pie-chart-fill"></i>
                        图书类别分布
                    </h6>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-outline-primary chart-refresh-btn">
                            <i class="bi bi-arrow-repeat"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body chart-body">
                    <div class="chart-info mb-2">
                        <div class="chart-summary">
                            <div class="summary-value">{{ book_count }}</div>
                            <div class="summary-label">总图书量</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <!-- 最近借阅 -->
        <div class="col-xl-6 mb-3">
            <div class="chart-card fade-in">
                <div class="card-header">
                    <h6 class="card-title">
                        <i class="bi bi-clock-history"></i>
                        最近借阅
                    </h6>
                    <div class="card-actions">
                        <a href="#" class="btn btn-sm btn-primary">
                            <i class="bi bi-list-ul me-1"></i>查看全部
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-admin table-hover mb-0" id="loanTable">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="reader">
                                        读者 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="book">
                                        图书 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="date">
                                        借阅日期 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="status">
                                        状态 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for loan in recent_loans %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-xs me-2 bg-primary text-white rounded-circle">
                                                {{ loan.reader.name[0] }}
                                            </div>
                                            <span>{{ loan.reader.name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 120px;" title="{{ loan.book.title }}">
                                            {{ loan.book.title }}
                                        </span>
                                    </td>
                                    <td>{{ loan.loan_date|datetime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if loan.return_date %}
                                        <span class="status-badge badge-success">
                                            <i class="bi bi-check-circle me-1"></i>已归还
                                        </span>
                                        {% elif loan.is_overdue() %}
                                        <span class="status-badge badge-danger">
                                            <i class="bi bi-exclamation-circle me-1"></i>已逾期
                                        </span>
                                        {% else %}
                                        <span class="status-badge badge-primary">
                                            <i class="bi bi-book me-1"></i>借阅中
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="p-2 text-end">
                        <small class="text-muted">显示最近 {{ recent_loans|length }} 条借阅记录</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 库存预警 -->
        <div class="col-xl-6 mb-3">
            <div class="chart-card fade-in" style="animation-delay: 0.1s;">
                <div class="card-header">
                    <h6 class="card-title">
                        <i class="bi bi-exclamation-triangle"></i>
                        库存预警
                    </h6>
                    <div class="card-actions">
                        <a href="#" class="btn btn-sm btn-warning">
                            <i class="bi bi-list-ul me-1"></i>查看全部
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-admin table-hover mb-0" id="bookTable">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="isbn">
                                        ISBN <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="title">
                                        书名 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="quantity">
                                        总数量 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="available">
                                        可借数量 <i class="bi bi-arrow-down-up text-muted ms-1"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for book in low_stock_books %}
                                <tr>
                                    <td>
                                        <span class="text-monospace">{{ book.isbn }}</span>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 120px;" title="{{ book.title }}">
                                            {{ book.title }}
                                        </span>
                                    </td>
                                    <td>{{ book.quantity }}</td>
                                    <td>
                                        <span class="status-badge badge-{{ 'danger' if book.available_quantity == 0 else 'warning' }}">
                                            {{ book.available_quantity }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="p-2 text-end">
                        <small class="text-muted">显示 {{ low_stock_books|length }} 条库存预警记录</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='chart.js/chart.umd.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 借阅趋势图表
        const loanTrendCtx = document.getElementById('loanTrendChart').getContext('2d');
        const loanTrendChart = new Chart(loanTrendCtx, {
            type: 'line',
            data: {
                labels: [
                    {% for item in loan_trend %}
                    '{{ item.date }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '借阅数量',
                    data: [
                        {% for item in loan_trend %}
                        {{ item.count }},
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(66, 153, 225, 0.2)',
                    borderColor: 'rgba(66, 153, 225, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    pointBackgroundColor: 'rgba(66, 153, 225, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(66, 153, 225, 1)',
                    pointHoverBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleFont: {
                            size: 13
                        },
                        bodyFont: {
                            size: 12
                        },
                        padding: 10,
                        cornerRadius: 5,
                        displayColors: false
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        // 图书类别分布图表
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryChart = new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    {% for item in category_stats %}
                    '{{ item.category_name }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in category_stats %}
                        {{ item.book_count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(66, 153, 225, 0.8)',
                        'rgba(72, 187, 120, 0.8)',
                        'rgba(246, 173, 85, 0.8)',
                        'rgba(237, 100, 166, 0.8)',
                        'rgba(159, 122, 234, 0.8)',
                        'rgba(237, 137, 54, 0.8)',
                        'rgba(56, 178, 172, 0.8)',
                        'rgba(113, 128, 150, 0.8)'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff',
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            padding: 15,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleFont: {
                            size: 13
                        },
                        bodyFont: {
                            size: 12
                        },
                        padding: 10,
                        cornerRadius: 5,
                        displayColors: false
                    }
                },
                cutout: '70%'
            }
        });

        // 表格搜索功能
        function setupTableSearch(inputId, tableId) {
            const searchInput = document.getElementById(inputId);
            const table = document.getElementById(tableId);

            if (!searchInput || !table) return;

            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(searchText) ? '' : 'none';
                });
            });
        }

        // 表格排序功能
        function setupTableSort(tableId) {
            const table = document.getElementById(tableId);
            if (!table) return;

            const headers = table.querySelectorAll('th.sortable');

            headers.forEach(header => {
                header.addEventListener('click', function() {
                    const sortBy = this.getAttribute('data-sort');
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));

                    // 切换排序方向
                    const isAscending = !this.classList.contains('sort-asc');

                    // 移除所有排序类
                    headers.forEach(h => {
                        h.classList.remove('sort-asc', 'sort-desc');
                    });

                    // 添加当前排序类
                    this.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

                    // 获取列索引
                    const columnIndex = Array.from(this.parentNode.children).indexOf(this);

                    // 排序行
                    rows.sort((a, b) => {
                        const aValue = a.children[columnIndex].textContent.trim();
                        const bValue = b.children[columnIndex].textContent.trim();

                        if (!isNaN(aValue) && !isNaN(bValue)) {
                            return isAscending ? aValue - bValue : bValue - aValue;
                        }

                        return isAscending
                            ? aValue.localeCompare(bValue)
                            : bValue.localeCompare(aValue);
                    });

                    // 重新添加排序后的行
                    rows.forEach(row => tbody.appendChild(row));
                });
            });
        }

        // 初始化表格功能
        setupTableSort('loanTable');
        setupTableSort('bookTable');

        // 图表周期选择器
        const periodButtons = document.querySelectorAll('.chart-period-selector .btn');
        periodButtons.forEach(button => {
            button.addEventListener('click', function() {
                periodButtons.forEach(btn => btn.classList.remove('active', 'btn-primary'));
                periodButtons.forEach(btn => btn.classList.add('btn-outline-primary'));
                this.classList.remove('btn-outline-primary');
                this.classList.add('active', 'btn-primary');

                // 获取选择的周期
                const period = this.getAttribute('data-period');

                // 模拟不同周期的数据
                let labels = [];
                let data = [];

                if (period === 'week') {
                    // 使用原始数据（周数据）
                    labels = [
                        {% for item in loan_trend %}
                        '{{ item.date }}',
                        {% endfor %}
                    ];
                    data = [
                        {% for item in loan_trend %}
                        {{ item.count }},
                        {% endfor %}
                    ];
                } else if (period === 'month') {
                    // 模拟月数据
                    labels = ['1月', '2月', '3月', '4月', '5月', '6月'];
                    data = [65, 59, 80, 81, 56, 55];
                } else if (period === 'year') {
                    // 模拟年数据
                    labels = ['2020', '2021', '2022', '2023', '2024'];
                    data = [280, 390, 430, 510, 350];
                }

                // 更新图表数据
                loanTrendChart.data.labels = labels;
                loanTrendChart.data.datasets[0].data = data;
                loanTrendChart.update();

                // 更新图表标题
                const periodText = period === 'week' ? '周' : (period === 'month' ? '月' : '年');
                document.querySelector('.chart-summary .summary-label').textContent = `${periodText}借阅量`;
            });
        });

        // 图表刷新按钮
        const refreshButton = document.querySelector('.chart-refresh-btn');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                // 添加旋转动画
                this.querySelector('i').classList.add('rotate-animation');

                // 模拟刷新延迟
                setTimeout(() => {
                    // 这里可以添加刷新图表数据的逻辑
                    categoryChart.update();

                    // 移除旋转动画
                    this.querySelector('i').classList.remove('rotate-animation');
                }, 800);
            });
        }
    });
</script>
<style>
    /* 旋转动画 */
    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .rotate-animation {
        animation: rotate 0.8s linear;
    }
</style>
{% endblock %}
