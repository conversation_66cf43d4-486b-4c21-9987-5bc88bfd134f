#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
读者API资源
"""

from flask import request, current_app
from flask_restful import Resource
from marshmallow import ValidationError

from .. import db
from ..models.reader import Reader, ReaderType
from ..schemas.reader_schema import ReaderSchema, ReaderTypeSchema
from ..utils.db import get_or_404


class ReaderListResource(Resource):
    """读者列表资源"""
    
    def get(self):
        """获取所有读者"""
        readers = Reader.query.all()
        return ReaderSchema(many=True).dump(readers)
    
    def post(self):
        """创建读者"""
        json_data = request.get_json()
        if not json_data:
            return {'message': '没有提供数据'}, 400
        
        try:
            reader_data = ReaderSchema().load(json_data)
        except ValidationError as err:
            return {'message': '数据验证错误', 'errors': err.messages}, 422
        
        # 检查读者ID是否已存在
        if Reader.query.get(reader_data.reader_id):
            return {'message': f'读者ID {reader_data.reader_id} 已存在'}, 409
        
        # 保存读者
        db.session.add(reader_data)
        db.session.commit()
        
        return ReaderSchema().dump(reader_data), 201


class ReaderResource(Resource):
    """读者资源"""
    
    def get(self, reader_id):
        """获取读者"""
        reader = get_or_404(Reader, reader_id, f'读者 {reader_id} 不存在')
        return ReaderSchema().dump(reader)
    
    def put(self, reader_id):
        """更新读者"""
        reader = get_or_404(Reader, reader_id, f'读者 {reader_id} 不存在')
        
        json_data = request.get_json()
        if not json_data:
            return {'message': '没有提供数据'}, 400
        
        try:
            reader_data = ReaderSchema().load(json_data, instance=reader, partial=True)
        except ValidationError as err:
            return {'message': '数据验证错误', 'errors': err.messages}, 422
        
        db.session.commit()
        
        return ReaderSchema().dump(reader_data)
    
    def delete(self, reader_id):
        """删除读者"""
        reader = get_or_404(Reader, reader_id, f'读者 {reader_id} 不存在')
        
        db.session.delete(reader)
        db.session.commit()
        
        return {'message': f'读者 {reader_id} 已删除'}
