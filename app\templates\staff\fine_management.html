{% extends "base.html" %}

{% block title %}罚款管理 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">罚款管理</h1>
            <p class="text-muted mb-0">管理读者的逾期罚款</p>
        </div>
        <a href="{{ url_for('staff.dashboard') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>返回仪表盘
        </a>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                未缴纳罚款
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ unpaid_fines.total }} 笔
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                罚款总额
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ¥{{ "%.2f"|format(unpaid_fines.items|sum(attribute='fine_amount') if unpaid_fines.items else 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-yen text-danger" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                平均罚款
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ¥{{ "%.2f"|format((unpaid_fines.items|sum(attribute='fine_amount') / unpaid_fines.items|length) if unpaid_fines.items else 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calculator text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 罚款列表 -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="bi bi-list-ul me-2"></i>未缴纳罚款列表
            </h6>
        </div>
        <div class="card-body">
            {% if unpaid_fines.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>借阅编号</th>
                            <th>读者信息</th>
                            <th>图书信息</th>
                            <th>借阅日期</th>
                            <th>应还日期</th>
                            <th>逾期天数</th>
                            <th>罚款金额</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for loan in unpaid_fines.items %}
                        <tr>
                            <td>
                                <span class="badge bg-primary">{{ loan.loan_id }}</span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ loan.reader.name }}</strong>
                                </div>
                                <small class="text-muted">{{ loan.reader_id }}</small><br>
                                <small class="text-muted">{{ loan.reader.department }}</small>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ loan.book.title }}</strong>
                                </div>
                                <small class="text-muted">{{ loan.book.author }}</small><br>
                                <small class="text-muted">ISBN: {{ loan.isbn }}</small>
                            </td>
                            <td>{{ loan.loan_date.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <span class="text-danger">{{ loan.due_date.strftime('%Y-%m-%d') }}</span>
                            </td>
                            <td>
                                {% if loan.return_date %}
                                    {% set overdue_days = (loan.return_date.date() - loan.due_date.date()).days %}
                                {% else %}
                                    {% set overdue_days = (moment().date() - loan.due_date.date()).days %}
                                {% endif %}
                                <span class="badge bg-warning">{{ overdue_days }}天</span>
                            </td>
                            <td>
                                <span class="text-danger fw-bold">¥{{ "%.2f"|format(loan.fine_amount) }}</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('staff.pay_fine', loan_id=loan.loan_id) }}" 
                                       class="btn btn-sm btn-success" 
                                       onclick="return confirm('确认收取罚款 ¥{{ "%.2f"|format(loan.fine_amount) }} 吗？')">
                                        <i class="bi bi-cash me-1"></i>收取
                                    </a>
                                    <a href="{{ url_for('staff.waive_fine', loan_id=loan.loan_id) }}" 
                                       class="btn btn-sm btn-warning"
                                       onclick="return confirm('确认免除罚款 ¥{{ "%.2f"|format(loan.fine_amount) }} 吗？')">
                                        <i class="bi bi-x-circle me-1"></i>免除
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if unpaid_fines.pages > 1 %}
            <nav aria-label="罚款列表分页">
                <ul class="pagination justify-content-center">
                    {% if unpaid_fines.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('staff.fine_management', page=unpaid_fines.prev_num) }}">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in unpaid_fines.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != unpaid_fines.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('staff.fine_management', page=page_num) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if unpaid_fines.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('staff.fine_management', page=unpaid_fines.next_num) }}">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-success">太好了！</h4>
                <p class="text-muted">目前没有未缴纳的罚款</p>
                <a href="{{ url_for('staff.dashboard') }}" class="btn btn-primary">
                    <i class="bi bi-house me-2"></i>返回仪表盘
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="bi bi-lightning-charge me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('staff.borrow_book') }}" class="btn btn-primary w-100">
                                <i class="bi bi-plus-circle me-2"></i>办理借书
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('staff.return_book') }}" class="btn btn-success w-100">
                                <i class="bi bi-arrow-return-left me-2"></i>办理还书
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('staff.loan_history') }}" class="btn btn-info w-100">
                                <i class="bi bi-clock-history me-2"></i>借阅历史
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('staff.reports') }}" class="btn btn-secondary w-100">
                                <i class="bi bi-graph-up me-2"></i>工作报表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
</style>
{% endblock %}
