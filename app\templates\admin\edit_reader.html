{% extends 'base.html' %}

{% block title %}编辑读者 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 编辑读者卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">编辑读者</h6>
                    <div>
                        <a href="{{ url_for('admin.reader_detail', reader_id=reader.reader_id) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回详情
                        </a>
                        <a href="{{ url_for('admin.readers') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-list"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('admin.edit_reader', reader_id=reader.reader_id) }}" method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="reader_id" class="form-label">读者编号</label>
                                <input type="text" class="form-control" id="reader_id" value="{{ reader.reader_id }}" disabled>
                                <small class="text-muted">读者编号不可修改</small>
                            </div>
                            <div class="col-md-6">
                                <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ reader.name }}" required>
                                <div class="invalid-feedback">请输入姓名</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="reader_type" class="form-label">读者类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="reader_type" name="reader_type" required>
                                    <option value="">请选择读者类型</option>
                                    {% for type in reader_types %}
                                    <option value="{{ type.reader_type }}" {% if reader.reader_type == type.reader_type %}selected{% endif %}>
                                        {{ type.reader_type }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">请选择读者类型</div>
                            </div>
                            <div class="col-md-6">
                                <label for="department" class="form-label">学院/部门</label>
                                <input type="text" class="form-control" id="department" name="department" value="{{ reader.department }}">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">手机号码</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="{{ reader.phone }}">
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">电子邮箱</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ reader.email }}">
                                <div class="invalid-feedback">请输入有效的电子邮箱地址</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> 重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 表单验证
    (function() {
        'use strict';

        // 获取所有需要验证的表单
        var forms = document.querySelectorAll('.needs-validation');

        // 循环并阻止提交
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                form.classList.add('was-validated');
            }, false);
        });
    })();
</script>
{% endblock %}
