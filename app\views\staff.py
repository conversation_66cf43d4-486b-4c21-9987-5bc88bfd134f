#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工作人员视图
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, desc, and_
from datetime import datetime, timedelta
import pytz

from .. import db
from ..models.reader import Reader, ReaderType
from ..models.book import Book, BookCategory
from ..models.loan import Loan
from ..models.staff import Staff
from ..utils.auth import staff_required
from ..utils.time_util import get_current_time, ensure_timezone

# 创建蓝图
staff_bp = Blueprint('staff', __name__, url_prefix='/staff')


@staff_bp.route('/dashboard')
@login_required
@staff_required
def dashboard():
    """工作人员仪表盘"""
    # 获取今日统计
    today = datetime.now(pytz.timezone('Asia/Shanghai')).date()
    today_start = datetime.combine(today, datetime.min.time())
    today_end = datetime.combine(today, datetime.max.time())
    
    # 今日借阅统计
    today_loans = Loan.query.filter(
        Loan.processed_by == current_user.staff_id,
        Loan.loan_date >= today_start,
        Loan.loan_date <= today_end
    ).count()
    
    # 今日归还统计
    today_returns = Loan.query.filter(
        Loan.processed_by == current_user.staff_id,
        Loan.return_date >= today_start,
        Loan.return_date <= today_end
    ).count()
    
    # 待处理逾期图书
    overdue_loans = Loan.query.filter(
        Loan.return_date.is_(None),
        Loan.due_date < datetime.now(pytz.timezone('Asia/Shanghai'))
    ).count()
    
    # 未缴纳罚款统计
    unpaid_fines = db.session.query(func.sum(Loan.fine_amount)).filter(
        Loan.fine_status == 'unpaid'
    ).scalar() or 0
    
    # 个人月度统计
    monthly_stats = current_user.get_monthly_stats()
    
    # 最近处理的借阅记录
    recent_loans = current_user.get_recent_processed_loans(5)
    
    return render_template('staff/dashboard.html',
                         today_loans=today_loans,
                         today_returns=today_returns,
                         overdue_loans=overdue_loans,
                         unpaid_fines=unpaid_fines,
                         monthly_stats=monthly_stats,
                         recent_loans=recent_loans)


@staff_bp.route('/borrow')
@login_required
@staff_required
def borrow_book():
    """办理借书页面"""
    return render_template('staff/borrow_book.html')


@staff_bp.route('/borrow', methods=['POST'])
@login_required
@staff_required
def process_borrow():
    """处理借书请求"""
    reader_id = request.form.get('reader_id')
    isbn = request.form.get('isbn')
    
    if not reader_id or not isbn:
        flash('请输入读者编号和图书ISBN', 'warning')
        return redirect(url_for('staff.borrow_book'))
    
    # 创建借阅记录
    loan, message = Loan.create_loan(reader_id, isbn, current_user.staff_id)
    
    if loan:
        flash(f'借阅成功！借阅编号：{loan.loan_id}', 'success')
    else:
        flash(f'借阅失败：{message}', 'danger')
    
    return redirect(url_for('staff.borrow_book'))


@staff_bp.route('/return')
@login_required
@staff_required
def return_book():
    """办理还书页面"""
    return render_template('staff/return_book.html')


@staff_bp.route('/return', methods=['POST'])
@login_required
@staff_required
def process_return():
    """处理还书请求"""
    loan_id = request.form.get('loan_id')
    reader_id = request.form.get('reader_id')
    isbn = request.form.get('isbn')
    
    # 查找借阅记录
    loan = None
    if loan_id:
        loan = Loan.query.get(loan_id)
    elif reader_id and isbn:
        loan = Loan.query.filter_by(
            reader_id=reader_id,
            isbn=isbn,
            return_date=None
        ).first()
    
    if not loan:
        flash('未找到对应的借阅记录', 'danger')
        return redirect(url_for('staff.return_book'))
    
    if loan.return_date:
        flash('该图书已经归还', 'warning')
        return redirect(url_for('staff.return_book'))
    
    # 处理归还
    if loan.process_return(current_user.staff_id):
        if loan.fine_amount > 0:
            flash(f'归还成功！产生罚款：{loan.fine_amount}元', 'warning')
        else:
            flash('归还成功！', 'success')
    else:
        flash('归还处理失败', 'danger')
    
    return redirect(url_for('staff.return_book'))


@staff_bp.route('/fines')
@login_required
@staff_required
def fine_management():
    """罚款管理页面"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # 查询未缴纳的罚款
    unpaid_fines = Loan.query.filter(
        Loan.fine_amount > 0,
        Loan.fine_status == 'unpaid'
    ).order_by(Loan.due_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('staff/fine_management.html', unpaid_fines=unpaid_fines)


@staff_bp.route('/pay_fine/<int:loan_id>')
@login_required
@staff_required
def pay_fine(loan_id):
    """缴纳罚款"""
    loan = Loan.query.get_or_404(loan_id)
    
    if loan.pay_fine(current_user.staff_id):
        flash(f'罚款缴纳成功！金额：{loan.fine_amount}元', 'success')
    else:
        flash('罚款缴纳失败', 'danger')
    
    return redirect(url_for('staff.fine_management'))


@staff_bp.route('/waive_fine/<int:loan_id>')
@login_required
@staff_required
def waive_fine(loan_id):
    """免除罚款"""
    loan = Loan.query.get_or_404(loan_id)
    reason = request.args.get('reason', '工作人员免除')
    
    if loan.waive_fine(current_user.staff_id, reason):
        flash(f'罚款免除成功！金额：{loan.fine_amount}元', 'success')
    else:
        flash('罚款免除失败', 'danger')
    
    return redirect(url_for('staff.fine_management'))


@staff_bp.route('/search_reader')
@login_required
@staff_required
def search_reader():
    """搜索读者"""
    query = request.args.get('q', '')
    if not query:
        return jsonify([])
    
    readers = Reader.query.filter(
        db.or_(
            Reader.reader_id.like(f'%{query}%'),
            Reader.name.like(f'%{query}%')
        ),
        Reader.status == 'valid'
    ).limit(10).all()
    
    return jsonify([
        {
            'reader_id': reader.reader_id,
            'name': reader.name,
            'department': reader.department,
            'reader_type': reader.reader_type,
            'current_loans': len(reader.get_current_loans())
        }
        for reader in readers
    ])


@staff_bp.route('/search_book')
@login_required
@staff_required
def search_book():
    """搜索图书"""
    query = request.args.get('q', '')
    if not query:
        return jsonify([])
    
    books = Book.query.filter(
        db.or_(
            Book.isbn.like(f'%{query}%'),
            Book.title.like(f'%{query}%'),
            Book.author.like(f'%{query}%')
        ),
        Book.available_quantity > 0
    ).limit(10).all()
    
    return jsonify([
        {
            'isbn': book.isbn,
            'title': book.title,
            'author': book.author,
            'publisher': book.publisher,
            'available_quantity': book.available_quantity
        }
        for book in books
    ])


@staff_bp.route('/loan_history')
@login_required
@staff_required
def loan_history():
    """借阅历史记录"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # 查询当前工作人员处理的借阅记录
    loans = Loan.query.filter_by(processed_by=current_user.staff_id)\
                      .order_by(Loan.processed_at.desc())\
                      .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('staff/loan_history.html', loans=loans)


@staff_bp.route('/reports')
@login_required
@staff_required
def reports():
    """工作报表"""
    # 获取本月统计
    now = datetime.now(pytz.timezone('Asia/Shanghai'))
    month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    
    # 本月处理的借阅数量
    monthly_loans = Loan.query.filter(
        Loan.processed_by == current_user.staff_id,
        Loan.processed_at >= month_start
    ).count()
    
    # 本月处理的归还数量
    monthly_returns = Loan.query.filter(
        Loan.processed_by == current_user.staff_id,
        Loan.return_date >= month_start
    ).count()
    
    # 本月处理的罚款金额
    monthly_fines = db.session.query(func.sum(Loan.fine_amount)).filter(
        Loan.processed_by == current_user.staff_id,
        Loan.processed_at >= month_start,
        Loan.fine_status.in_(['paid', 'waived'])
    ).scalar() or 0
    
    return render_template('staff/reports.html',
                         monthly_loans=monthly_loans,
                         monthly_returns=monthly_returns,
                         monthly_fines=monthly_fines)


@staff_bp.route('/profile')
@login_required
@staff_required
def profile():
    """个人资料"""
    return render_template('staff/profile.html')


@staff_bp.route('/profile', methods=['POST'])
@login_required
@staff_required
def update_profile():
    """更新个人资料"""
    phone = request.form.get('phone')
    
    if phone:
        current_user.update_info(phone=phone)
        flash('个人资料更新成功', 'success')
    else:
        flash('请输入有效的电话号码', 'warning')
    
    return redirect(url_for('staff.profile'))
