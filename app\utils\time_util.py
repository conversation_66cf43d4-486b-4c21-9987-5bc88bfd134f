#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
时间工具
"""

from datetime import datetime, timedelta
import pytz


def get_current_time():
    """获取当前时间（中国标准时间）

    返回:
        当前的日期时间对象（带有中国标准时间时区信息）
    """
    return datetime.now(pytz.timezone('Asia/Shanghai'))


def format_datetime(dt, format='%Y-%m-%d %H:%M:%S'):
    """格式化日期时间

    参数:
        dt: 日期时间对象
        format: 格式化字符串

    返回:
        格式化后的日期时间字符串
    """
    if dt is None:
        return ''

    # 确保时区为中国标准时间
    dt = ensure_timezone(dt)

    return dt.strftime(format)


def parse_datetime(dt_str, format='%Y-%m-%d %H:%M:%S'):
    """解析日期时间字符串

    参数:
        dt_str: 日期时间字符串
        format: 日期时间格式

    返回:
        解析后的日期时间对象（带有中国标准时间时区信息）
    """
    if not dt_str:
        return None

    dt = datetime.strptime(dt_str, format)

    # 设置时区为中国标准时间
    return pytz.timezone('Asia/Shanghai').localize(dt)


def ensure_timezone(dt, timezone_name='Asia/Shanghai'):
    """确保日期时间对象有时区信息

    参数:
        dt: 日期时间对象
        timezone_name: 时区名称，默认为中国标准时间

    返回:
        带有时区信息的日期时间对象
    """
    if dt is None:
        return None

    if dt.tzinfo is None:
        return pytz.timezone(timezone_name).localize(dt)
    return dt


def add_days(dt, days):
    """添加天数

    参数:
        dt: 日期时间对象
        days: 要添加的天数

    返回:
        添加指定天数后的日期时间对象
    """
    if dt is None:
        return None

    # 确保时区为中国标准时间
    dt = ensure_timezone(dt)

    # 直接使用timedelta添加天数
    return dt + timedelta(days=days)
