#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
读者视图
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from sqlalchemy import or_

from .. import db
from ..models.reader import Reader
from ..models.book import Book, BookCategory
from ..models.loan import Loan
from ..utils.auth import reader_required
from ..utils.time_util import get_current_time, ensure_timezone

# 创建蓝图
reader_bp = Blueprint('reader', __name__, url_prefix='/reader')


@reader_bp.route('/dashboard')
@login_required
@reader_required
def dashboard():
    """读者仪表盘"""
    # 获取当前借阅
    current_loans = current_user.get_current_loans()

    # 获取逾期借阅
    overdue_loans = current_user.get_overdue_loans()

    # 获取借阅历史
    loan_history = current_user.get_loan_history()

    # 获取可借数量
    max_books = current_user.reader_type_info.max_books
    current_borrowed = len(current_loans)
    available_to_borrow = max_books - current_borrowed

    return render_template(
        'reader/dashboard.html',
        current_loans=current_loans,
        overdue_loans=overdue_loans,
        loan_history=loan_history,
        max_books=max_books,
        current_borrowed=current_borrowed,
        available_to_borrow=available_to_borrow
    )


@reader_bp.route('/profile')
@login_required
@reader_required
def profile():
    """个人信息"""
    return render_template('reader/profile.html')


@reader_bp.route('/books')
@login_required
@reader_required
def books():
    """图书检索"""
    # 获取搜索参数
    search_query = request.args.get('q', '')
    category_id = request.args.get('category', '')
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 构建查询
    query = Book.query

    # 添加搜索条件
    if search_query:
        query = query.filter(
            or_(
                Book.title.like(f'%{search_query}%'),
                Book.author.like(f'%{search_query}%'),
                Book.publisher.like(f'%{search_query}%'),
                Book.isbn.like(f'%{search_query}%')
            )
        )

    if category_id:
        query = query.filter(Book.book_category_id == category_id)

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    books = pagination.items

    # 获取所有图书类别
    categories = BookCategory.query.all()

    # 获取当前用户已借阅的图书ISBN列表
    borrowed_books = {}
    current_loans = current_user.get_current_loans()
    for loan in current_loans:
        borrowed_books[loan.isbn] = loan.loan_id

    return render_template(
        'reader/books.html',
        books=books,
        pagination=pagination,
        search_query=search_query,
        category_id=category_id,
        categories=categories,
        borrowed_books=borrowed_books
    )


@reader_bp.route('/book/<string:isbn>')
@login_required
@reader_required
def book_detail(isbn):
    """图书详情"""
    book = Book.query.get_or_404(isbn)

    # 检查当前用户是否已借阅此图书
    current_loan = Loan.query.filter_by(
        reader_id=current_user.reader_id,
        isbn=isbn,
        return_date=None
    ).first()

    return render_template(
        'reader/book_detail.html',
        book=book,
        current_loan=current_loan
    )


@reader_bp.route('/loans')
@login_required
@reader_required
def loans():
    """借阅记录"""
    # 获取当前借阅
    current_loans = current_user.get_current_loans()

    # 获取借阅历史
    loan_history = current_user.get_loan_history()

    return render_template(
        'reader/loans.html',
        current_loans=current_loans,
        loan_history=loan_history
    )


@reader_bp.route('/loan/<int:loan_id>/renew', methods=['POST'])
@login_required
@reader_required
def renew_loan(loan_id):
    """续借"""
    loan = Loan.query.get_or_404(loan_id)

    # 检查是否是当前用户的借阅
    if loan.reader_id != current_user.reader_id:
        flash('您没有权限续借此图书', 'danger')
        return redirect(url_for('reader.loans'))

    # 检查是否已归还
    if loan.return_date is not None:
        flash('此图书已归还，不能续借', 'danger')
        return redirect(url_for('reader.loans'))

    # 检查是否逾期
    if loan.is_overdue():
        flash('此图书已逾期，不能续借', 'danger')
        return redirect(url_for('reader.loans'))

    # 获取已续借次数和最大续借次数
    renewal_count = loan.get_renewal_count()
    max_renewals = current_user.reader_type_info.max_renewals

    # 检查是否已达到最大续借次数
    if renewal_count >= max_renewals:
        flash(f'续借失败，您已达到最大续借次数({max_renewals}次)', 'danger')
        return redirect(url_for('reader.loans'))

    # 续借
    if not loan.renew(current_user):
        flash('续借失败', 'danger')
        return redirect(url_for('reader.loans'))

    db.session.commit()
    flash('续借成功', 'success')
    return redirect(url_for('reader.loans'))


@reader_bp.route('/book/<string:isbn>/borrow', methods=['POST'])
@login_required
@reader_required
def borrow_book(isbn):
    """借阅图书"""
    # 获取图书
    book = Book.query.get_or_404(isbn)

    # 检查是否已借阅此图书
    existing_loan = Loan.query.filter_by(
        reader_id=current_user.reader_id,
        isbn=isbn,
        return_date=None
    ).first()

    if existing_loan:
        flash('您已借阅此图书，不能重复借阅', 'danger')
        return redirect(url_for('reader.book_detail', isbn=isbn))

    # 检查图书是否可借
    if not book.is_available():
        flash('此图书已无可借数量', 'danger')
        return redirect(url_for('reader.book_detail', isbn=isbn))

    # 检查读者是否可以借书
    if not current_user.can_borrow():
        flash('您已达到最大借阅数量，不能再借阅更多图书', 'danger')
        return redirect(url_for('reader.book_detail', isbn=isbn))

    # 创建借阅记录
    from datetime import timedelta

    now = get_current_time()
    borrow_duration = current_user.reader_type_info.borrow_duration
    loan = Loan(
        reader_id=current_user.reader_id,
        isbn=isbn,
        loan_date=now,
        due_date=now + timedelta(days=borrow_duration)
    )

    # 更新图书可借数量
    book.available_quantity -= 1

    db.session.add(loan)
    db.session.commit()

    flash('图书借阅成功', 'success')
    return redirect(url_for('reader.loans'))


@reader_bp.route('/loan/<int:loan_id>/return', methods=['GET', 'POST'])
@login_required
@reader_required
def return_book(loan_id):
    """归还图书确认页面"""
    loan = Loan.query.get_or_404(loan_id)

    # 检查是否是当前用户的借阅
    if loan.reader_id != current_user.reader_id:
        flash('您没有权限归还此图书', 'danger')
        return redirect(url_for('reader.loans'))

    # 检查是否已归还
    if loan.return_date is not None:
        flash('此图书已归还', 'warning')
        return redirect(url_for('reader.loans'))

    # 获取当前时间
    now = get_current_time()

    # 显示归还确认页面
    return render_template('reader/return_confirm.html', loan=loan, now=now)


@reader_bp.route('/loan/<int:loan_id>/confirm_return', methods=['POST'])
@login_required
@reader_required
def confirm_return(loan_id):
    """确认归还图书"""
    loan = Loan.query.get_or_404(loan_id)

    # 检查是否是当前用户的借阅
    if loan.reader_id != current_user.reader_id:
        flash('您没有权限归还此图书', 'danger')
        return redirect(url_for('reader.loans'))

    # 检查是否已归还
    if loan.return_date is not None:
        flash('此图书已归还', 'warning')
        return redirect(url_for('reader.loans'))

    # 设置归还日期
    now = get_current_time()
    loan.return_date = now

    # 计算罚款金额（如果逾期）
    if loan.is_overdue():
        fine_amount = loan.calculate_fine()
        loan.fine_amount = fine_amount
        flash(f'图书已逾期，产生罚款 {fine_amount} 元，请到图书馆缴纳', 'warning')

    # 更新图书可借数量
    book = loan.book
    book.available_quantity += 1

    db.session.commit()

    flash('图书归还成功', 'success')
    return redirect(url_for('reader.loans'))
