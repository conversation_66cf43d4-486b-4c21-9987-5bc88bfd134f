{% extends "base.html" %}

{% block title %}工作人员仪表盘 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">工作人员仪表盘</h1>
            <p class="text-muted mb-0">欢迎，{{ current_user.name }}（{{ current_user.staff_id }}）</p>
        </div>
        <div>
            <span class="badge bg-success">{{ current_user.department or '未分配部门' }}</span>
            <span class="badge bg-info">{{ current_user.position or '未分配职位' }}</span>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                今日借阅
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_loans }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-book-half text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                今日归还
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_returns }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-arrow-return-left text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                逾期图书
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overdue_loans }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                未缴罚款
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">¥{{ "%.2f"|format(unpaid_fines) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-yen text-danger" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 快速操作 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-lightning-charge me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="{{ url_for('staff.borrow_book') }}" class="btn btn-primary btn-lg w-100">
                                <i class="bi bi-plus-circle me-2"></i>办理借书
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{{ url_for('staff.return_book') }}" class="btn btn-success btn-lg w-100">
                                <i class="bi bi-arrow-return-left me-2"></i>办理还书
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{{ url_for('staff.fine_management') }}" class="btn btn-warning btn-lg w-100">
                                <i class="bi bi-currency-yen me-2"></i>罚款管理
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{{ url_for('staff.reports') }}" class="btn btn-info btn-lg w-100">
                                <i class="bi bi-graph-up me-2"></i>工作报表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 月度统计 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-calendar-month me-2"></i>本月工作统计
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <div class="h4 text-primary">{{ monthly_stats.loans_processed }}</div>
                                <small class="text-muted">处理借阅</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <div class="h4 text-success">{{ current_user.get_work_days() }}</div>
                                <small class="text-muted">工作天数</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-info">{{ current_user.get_processed_loans_count() }}</div>
                            <small class="text-muted">总处理数</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近处理的借阅记录 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-clock-history me-2"></i>最近处理的借阅记录
                    </h6>
                    <a href="{{ url_for('staff.loan_history') }}" class="btn btn-sm btn-outline-primary">
                        查看全部
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_loans %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>借阅编号</th>
                                    <th>读者</th>
                                    <th>图书</th>
                                    <th>操作类型</th>
                                    <th>处理时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for loan in recent_loans %}
                                <tr>
                                    <td>{{ loan.loan_id }}</td>
                                    <td>
                                        <div>{{ loan.reader.name }}</div>
                                        <small class="text-muted">{{ loan.reader_id }}</small>
                                    </td>
                                    <td>
                                        <div>{{ loan.book.title }}</div>
                                        <small class="text-muted">{{ loan.isbn }}</small>
                                    </td>
                                    <td>
                                        {% if loan.return_date %}
                                            <span class="badge bg-success">归还</span>
                                        {% else %}
                                            <span class="badge bg-primary">借阅</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ loan.processed_at.strftime('%Y-%m-%d %H:%M') if loan.processed_at else '-' }}</td>
                                    <td>
                                        {% if loan.return_date %}
                                            {% if loan.fine_amount > 0 %}
                                                <span class="badge bg-warning">罚款 ¥{{ "%.2f"|format(loan.fine_amount) }}</span>
                                            {% else %}
                                                <span class="badge bg-success">正常归还</span>
                                            {% endif %}
                                        {% elif loan.is_overdue() %}
                                            <span class="badge bg-danger">逾期 {{ loan.get_overdue_days() }}天</span>
                                        {% else %}
                                            <span class="badge bg-info">借阅中</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">暂无处理记录</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}
</style>
{% endblock %}
