<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="512" y2="512" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1a3a5f"/>
      <stop offset="1" stop-color="#4299e1"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="10" flood-color="#000" flood-opacity="0.2"/>
    </filter>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="url(#paint0_linear)" filter="url(#shadow)"/>

  <!-- 书本图标 -->
  <g transform="translate(106, 106) scale(0.6)">
    <!-- 书本底部 -->
    <path d="M100 50C100 77.6142 122.386 100 150 100H400V400H150C122.386 400 100 377.614 100 350V50Z" fill="white" filter="url(#shadow)"/>

    <!-- 书本封面 -->
    <path d="M150 50H400V350H150C122.386 350 100 327.614 100 300V100C100 72.3858 122.386 50 150 50Z" fill="white" filter="url(#shadow)"/>

    <!-- 书本装饰线 -->
    <path d="M180 100H370M180 150H370M180 200H370M180 250H370M180 300H370" stroke="#1a3a5f" stroke-width="10" stroke-linecap="round"/>

    <!-- 书签 -->
    <path d="M350 50V150L325 125L300 150V50H350Z" fill="#4299e1" filter="url(#shadow)"/>
  </g>

  <!-- 毕业帽 -->
  <g transform="translate(256, 150) scale(0.25) rotate(15)">
    <!-- 帽子底座 -->
    <rect x="-200" y="100" width="400" height="50" rx="10" fill="#1a3a5f"/>

    <!-- 帽子顶部 -->
    <path d="M-200 0L200 0L0 -200L-200 0Z" fill="#1a3a5f"/>

    <!-- 帽子流苏 -->
    <path d="M0 -200V-250" stroke="#4299e1" stroke-width="20" stroke-linecap="round"/>
    <circle cx="0" cy="-270" r="30" fill="#4299e1"/>
  </g>

  <!-- 光效 -->
  <circle cx="180" cy="180" r="20" fill="white" opacity="0.3"/>
  <circle cx="200" cy="150" r="10" fill="white" opacity="0.2"/>
  <circle cx="350" cy="300" r="15" fill="white" opacity="0.2"/>
  <circle cx="320" cy="350" r="8" fill="white" opacity="0.15"/>
</svg>
