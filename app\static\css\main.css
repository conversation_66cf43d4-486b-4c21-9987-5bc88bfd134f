/* 
 * 图书借阅管理系统主样式文件
 * 统一整个系统的UI风格，包括读者界面和管理员界面
 */

/* 基础变量 */
:root {
    --primary-dark: #1a3a5f;
    --primary-light: #4299e1;
    --primary-gradient: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
    --secondary-color: #718096;
    --success-color: #38a169;
    --danger-color: #e53e3e;
    --warning-color: #ecc94b;
    --info-color: #4299e1;
    --light-color: #f8f9fa;
    --dark-color: #2d3748;
    --text-color: #333333;
    --text-muted: #718096;
    --border-color: #e2e8f0;
    --sidebar-width: 280px;
    --header-height: 60px;
    --footer-height: 50px;
    --content-padding: 1.5rem;
    --transition-speed: 0.3s;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --background-color: #f8f9fa;
    --card-border-radius: 0.75rem;
    --btn-border-radius: 0.5rem;
    --input-border-radius: 0.5rem;
}

/* 基础样式 */
body {
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    font-size: 1rem;
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

a {
    color: var(--primary-dark);
    text-decoration: none;
    transition: all var(--transition-speed);
}

a:hover {
    color: var(--primary-light);
    text-decoration: none;
}

/* 布局样式 */
.sidebar {
    width: var(--sidebar-width);
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    background: var(--primary-gradient);
    color: white;
    z-index: 1000;
    transition: all var(--transition-speed);
    box-shadow: none;
    overflow-y: auto;
    border-right: none;
}

.sidebar-collapsed {
    left: calc(-1 * var(--sidebar-width) + 60px);
}

.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: margin-left var(--transition-speed);
    width: calc(100% - var(--sidebar-width));
    background-color: var(--background-color);
}

.main-content-expanded {
    margin-left: 60px;
    width: calc(100% - 60px);
}

.content-wrapper {
    flex: 1;
    padding: var(--content-padding);
    padding-top: calc(var(--header-height) + 0.75rem);
    padding-bottom: calc(var(--footer-height) + 0.75rem);
    width: 100%;
}

/* 侧边栏样式 */
.sidebar-brand {
    height: var(--header-height);
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    background: rgba(0, 0, 0, 0.1);
}

.sidebar-brand-icon {
    margin-right: 0.75rem;
}

.sidebar-brand-text {
    font-weight: 600;
    font-size: 1.2rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-divider {
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    margin: 1rem 0;
}

.sidebar-heading {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 600;
}

.sidebar .nav-item {
    margin-bottom: 0.25rem;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    transition: all var(--transition-speed);
    border-left: 3px solid transparent;
    font-size: 1.1rem;
}

.sidebar .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    border-left-color: white;
}

.sidebar .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border-left-color: white;
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 1.5rem;
    text-align: center;
}

/* 导航栏样式 */
.navbar {
    height: var(--header-height);
    background-color: white;
    box-shadow: var(--box-shadow);
    padding: 0 var(--content-padding);
    z-index: 900;
    width: 100%;
    min-height: auto;
}

.navbar h1.h3, .navbar h1.h4 {
    font-size: 1.8rem;
    margin-bottom: 0;
    color: var(--dark-color);
    font-weight: 600;
}

.navbar .dropdown-menu {
    box-shadow: var(--box-shadow);
    border: none;
    border-radius: var(--card-border-radius);
    overflow: hidden;
}

.navbar .dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all var(--transition-speed);
}

.navbar .dropdown-item:hover {
    background-color: var(--light-color);
}

.navbar .dropdown-item i {
    margin-right: 0.75rem;
    color: var(--primary-dark);
}

.navbar .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 0.5rem;
}

/* 页脚样式 */
.app-footer {
    height: var(--footer-height);
    background-color: white;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 0;
    width: calc(100% - var(--sidebar-width));
    transition: width var(--transition-speed);
    z-index: 900;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    font-size: 1.05rem;
}

.app-footer-expanded {
    width: calc(100% - 60px);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    transition: transform var(--transition-speed);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h5, .card-header h6 {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--dark-color);
}

.card-header i {
    margin-right: 0.5rem;
    color: var(--primary-light);
}

.card-body {
    padding: 1.5rem;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--light-color);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.05rem;
    padding: 1rem 1.5rem;
    border-top: none;
    border-bottom-width: 1px;
    color: var(--dark-color);
}

.table tbody td {
    padding: 1rem 1.5rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 1rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(66, 153, 225, 0.05);
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1.25rem;
    border-radius: var(--btn-border-radius);
    font-weight: 500;
    transition: all var(--transition-speed);
    font-size: 1rem;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.9rem;
}

.btn-primary {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-primary:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-light);
}

.btn-outline-primary {
    color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* 徽章样式 */
.badge {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    font-size: 0.9rem;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out forwards;
}

@keyframes slideInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.zoom-in {
    animation: zoomIn 0.5s ease-out forwards;
}

@keyframes zoomIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

/* 响应式调整 */
@media (max-width: 992px) {
    :root {
        --sidebar-width: 0px;
    }

    .sidebar {
        left: -280px;
    }

    .sidebar.show {
        left: 0;
        width: 280px;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .main-content-expanded {
        width: 100%;
    }

    .app-footer {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .content-wrapper {
        padding: 1rem;
    }
    
    .card-header, .card-body {
        padding: 1rem;
    }
    
    .table thead th, .table tbody td {
        padding: 0.75rem 1rem;
    }
}
