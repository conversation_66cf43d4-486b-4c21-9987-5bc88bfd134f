{% extends 'base.html' %}

{% block title %}图书详情 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8">
            <!-- 图书信息卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">图书信息</h6>
                    <div>
                        <a href="{{ url_for('admin.books') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('admin.edit_book', isbn=book.isbn) }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-pencil"></i> 编辑
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 20%">ISBN</th>
                                    <td>{{ book.isbn }}</td>
                                </tr>
                                <tr>
                                    <th>书名</th>
                                    <td>{{ book.title }}</td>
                                </tr>
                                <tr>
                                    <th>作者</th>
                                    <td>{{ book.author }}</td>
                                </tr>
                                <tr>
                                    <th>出版社</th>
                                    <td>{{ book.publisher }}</td>
                                </tr>
                                <tr>
                                    <th>出版年份</th>
                                    <td>{{ book.year }}</td>
                                </tr>
                                <tr>
                                    <th>图书类别</th>
                                    <td>
                                        <a href="{{ url_for('admin.category_detail', category_id=book.book_category_id) }}">
                                            {{ book.category.category_name }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>总数量</th>
                                    <td>{{ book.quantity }}</td>
                                </tr>
                                <tr>
                                    <th>可借数量</th>
                                    <td>
                                        {% if book.available_quantity > 0 %}
                                        <span class="badge bg-success">{{ book.available_quantity }}</span>
                                        {% else %}
                                        <span class="badge bg-danger">0</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 图书简介 -->
                    <div class="mt-4">
                        <h5 class="font-weight-bold">图书简介</h5>
                        <p class="text-muted">
                            这是《{{ book.title }}》的简介。由于数据库中没有存储图书简介，这里显示的是默认文本。
                            在实际应用中，应该从数据库中读取图书的详细介绍，并在此处显示。
                        </p>
                    </div>

                    <!-- 删除图书按钮 -->
                    <div class="mt-4 text-end">
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteBookModal">
                            <i class="bi bi-trash"></i> 删除图书
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 借阅状态卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">借阅状态</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        {% if book.available_quantity > 0 %}
                        <div class="mb-3">
                            <span class="badge bg-success p-2">可借阅</span>
                        </div>
                        <p class="text-success">当前有 {{ book.available_quantity }} 本可借</p>
                        {% else %}
                        <div class="mb-3">
                            <span class="badge bg-danger p-2">不可借阅</span>
                        </div>
                        <p class="text-danger">当前无可借图书</p>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        {% if book.available_quantity > 0 %}
                        <a href="{{ url_for('admin.create_loan') }}" class="btn btn-primary">
                            <i class="bi bi-journal-plus"></i> 借出此书
                        </a>
                        {% else %}
                        <button class="btn btn-secondary" disabled>
                            <i class="bi bi-journal-x"></i> 暂不可借
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 借阅信息卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">借阅信息</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            罚款金额
                            <span class="badge bg-warning text-dark rounded-pill">{{ book.category.fine_amount }} 元/天</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            当前借出
                            <span class="badge bg-primary rounded-pill">{{ book.quantity - book.available_quantity }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 最近借阅记录 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">最近借阅记录</h6>
                </div>
                <div class="card-body">
                    {% if book.loans %}
                    <div class="list-group list-group-flush">
                        {% for loan in book.loans[:5] %}
                        <a href="{{ url_for('admin.loan_detail', loan_id=loan.loan_id) }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ loan.reader.name }}</h6>
                                <small>
                                    {% if loan.return_date %}
                                    <span class="badge bg-success">已归还</span>
                                    {% elif loan.is_overdue() %}
                                    <span class="badge bg-danger">已逾期</span>
                                    {% else %}
                                    <span class="badge bg-primary">借阅中</span>
                                    {% endif %}
                                </small>
                            </div>
                            <p class="mb-1">借阅日期: {{ loan.loan_date|datetime('%Y-%m-%d') }}</p>
                            <small>应还日期: {{ loan.due_date|datetime('%Y-%m-%d') }}</small>
                        </a>
                        {% endfor %}
                    </div>
                    {% if book.loans|length > 5 %}
                    <div class="text-center mt-3">
                        <a href="{{ url_for('admin.loans') }}?q={{ book.isbn }}" class="btn btn-sm btn-outline-primary">
                            查看全部借阅记录
                        </a>
                    </div>
                    {% endif %}
                    {% else %}
                    <p class="text-muted mb-0">暂无借阅记录</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除图书确认模态框 -->
<div class="modal fade" id="deleteBookModal" tabindex="-1" aria-labelledby="deleteBookModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteBookModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除图书《{{ book.title }}》吗？此操作不可逆。</p>
                <p class="text-danger">注意：如果此图书有关联的借阅记录，将无法删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('admin.delete_book', isbn=book.isbn) }}" method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
