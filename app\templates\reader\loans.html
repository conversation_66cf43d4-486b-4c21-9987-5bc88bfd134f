{% extends 'base.html' %}

{% block title %}借阅记录 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/reader.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <!-- 当前借阅 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">当前借阅</h6>
        </div>
        <div class="card-body">
            {% if current_loans %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>图书名称</th>
                            <th>ISBN</th>
                            <th>借阅日期</th>
                            <th>应还日期</th>
                            <th>续借次数</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for loan in current_loans %}
                        <tr>
                            <td>{{ loan.book.title }}</td>
                            <td>{{ loan.isbn }}</td>
                            <td>{{ loan.loan_date|datetime('%Y-%m-%d') }}</td>
                            <td>{{ loan.due_date|datetime('%Y-%m-%d') }}</td>
                            <td>
                                {% set renewal_count = loan.get_renewal_count() %}
                                {% set max_renewals = current_user.reader_type_info.max_renewals %}
                                <span class="badge {% if renewal_count >= max_renewals %}bg-danger{% else %}bg-info{% endif %}">
                                    {{ renewal_count }}/{{ max_renewals }}
                                </span>
                            </td>
                            <td>
                                {% if loan.is_overdue() %}
                                <span class="badge bg-danger">已逾期 {{ loan.get_overdue_days() }} 天</span>
                                {% else %}
                                <span class="badge bg-success">正常</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if not loan.is_overdue() %}
                                <form action="{{ url_for('reader.renew_loan', loan_id=loan.loan_id) }}" method="post" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    {% set renewal_count = loan.get_renewal_count() %}
                                    {% set max_renewals = current_user.reader_type_info.max_renewals %}
                                    <button type="submit" class="btn btn-sm btn-primary" {% if renewal_count >= max_renewals %}disabled title="已达到最大续借次数"{% endif %}>
                                        续借
                                    </button>
                                </form>
                                {% endif %}
                                <a href="{{ url_for('reader.return_book', loan_id=loan.loan_id) }}" class="btn btn-sm btn-warning">
                                    <i class="bi bi-journal-arrow-up"></i> 归还
                                </a>
                                <a href="{{ url_for('reader.book_detail', isbn=loan.isbn) }}" class="btn btn-sm btn-info">
                                    <i class="bi bi-info-circle"></i> 详情
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="bi bi-journal-text fa-4x text-gray-300 mb-3"></i>
                <p class="text-gray-500">您当前没有借阅任何图书</p>
                <a href="{{ url_for('reader.books') }}" class="btn btn-primary">
                    <i class="bi bi-search"></i> 查找图书
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 借阅历史 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">借阅历史</h6>
        </div>
        <div class="card-body">
            {% if loan_history %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>图书名称</th>
                            <th>ISBN</th>
                            <th>借阅日期</th>
                            <th>应还日期</th>
                            <th>实际归还日期</th>
                            <th>罚款金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for loan in loan_history %}
                        <tr>
                            <td>{{ loan.book.title }}</td>
                            <td>{{ loan.isbn }}</td>
                            <td>{{ loan.loan_date|datetime('%Y-%m-%d') }}</td>
                            <td>{{ loan.due_date|datetime('%Y-%m-%d') }}</td>
                            <td>{{ loan.return_date|datetime('%Y-%m-%d') }}</td>
                            <td>
                                {% if loan.fine_amount and loan.fine_amount > 0 %}
                                <span class="text-danger">{{ loan.fine_amount }} 元</span>
                                {% else %}
                                <span class="text-success">0 元</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="bi bi-journal-check fa-4x text-gray-300 mb-3"></i>
                <p class="text-gray-500">您没有借阅历史记录</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
