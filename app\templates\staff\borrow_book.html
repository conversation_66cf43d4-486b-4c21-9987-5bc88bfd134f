{% extends "base.html" %}

{% block title %}办理借书 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">办理借书</h1>
            <p class="text-muted mb-0">为读者办理图书借阅手续</p>
        </div>
        <a href="{{ url_for('staff.dashboard') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>返回仪表盘
        </a>
    </div>

    <div class="row">
        <!-- 借书表单 -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-plus-circle me-2"></i>借书信息
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('staff.process_borrow') }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="reader_id" class="form-label">
                                    <i class="bi bi-person-vcard me-1"></i>读者编号
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="reader_id" name="reader_id" 
                                           placeholder="输入读者编号或姓名搜索" required autocomplete="off">
                                    <button type="button" class="btn btn-outline-secondary" id="search_reader_btn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <div id="reader_suggestions" class="list-group mt-1" style="display: none;"></div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="isbn" class="form-label">
                                    <i class="bi bi-book me-1"></i>图书ISBN
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="isbn" name="isbn" 
                                           placeholder="输入ISBN或书名搜索" required autocomplete="off">
                                    <button type="button" class="btn btn-outline-secondary" id="search_book_btn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <div id="book_suggestions" class="list-group mt-1" style="display: none;"></div>
                            </div>
                        </div>

                        <!-- 读者信息显示 -->
                        <div id="reader_info" class="alert alert-info" style="display: none;">
                            <h6><i class="bi bi-person-check me-2"></i>读者信息</h6>
                            <div id="reader_details"></div>
                        </div>

                        <!-- 图书信息显示 -->
                        <div id="book_info" class="alert alert-success" style="display: none;">
                            <h6><i class="bi bi-book-half me-2"></i>图书信息</h6>
                            <div id="book_details"></div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-secondary me-md-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>确认借阅
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 借阅规则 -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="bi bi-info-circle me-2"></i>借阅规则
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="bi bi-calendar-check text-primary me-2"></i>
                            借阅期限：30天
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-currency-yen text-warning me-2"></i>
                            逾期罚款：每天0.5元
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-person-x text-danger me-2"></i>
                            证件失效无法借阅
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            有逾期图书无法借阅
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-cash text-danger me-2"></i>
                            有未缴罚款无法借阅
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="bi bi-lightning-charge me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('staff.return_book') }}" class="btn btn-success">
                            <i class="bi bi-arrow-return-left me-2"></i>办理还书
                        </a>
                        <a href="{{ url_for('staff.fine_management') }}" class="btn btn-warning">
                            <i class="bi bi-currency-yen me-2"></i>罚款管理
                        </a>
                        <a href="{{ url_for('staff.loan_history') }}" class="btn btn-info">
                            <i class="bi bi-clock-history me-2"></i>借阅历史
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const readerInput = document.getElementById('reader_id');
    const isbnInput = document.getElementById('isbn');
    const readerSuggestions = document.getElementById('reader_suggestions');
    const bookSuggestions = document.getElementById('book_suggestions');
    const readerInfo = document.getElementById('reader_info');
    const bookInfo = document.getElementById('book_info');
    const readerDetails = document.getElementById('reader_details');
    const bookDetails = document.getElementById('book_details');

    // 读者搜索
    let readerSearchTimeout;
    readerInput.addEventListener('input', function() {
        clearTimeout(readerSearchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            readerSuggestions.style.display = 'none';
            readerInfo.style.display = 'none';
            return;
        }

        readerSearchTimeout = setTimeout(() => {
            fetch(`{{ url_for('staff.search_reader') }}?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    readerSuggestions.innerHTML = '';
                    if (data.length > 0) {
                        data.forEach(reader => {
                            const item = document.createElement('a');
                            item.className = 'list-group-item list-group-item-action';
                            item.href = '#';
                            item.innerHTML = `
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${reader.name} (${reader.reader_id})</h6>
                                    <small>已借 ${reader.current_loans} 本</small>
                                </div>
                                <p class="mb-1">${reader.department}</p>
                                <small>${reader.reader_type}</small>
                            `;
                            item.addEventListener('click', function(e) {
                                e.preventDefault();
                                readerInput.value = reader.reader_id;
                                readerSuggestions.style.display = 'none';
                                showReaderInfo(reader);
                            });
                            readerSuggestions.appendChild(item);
                        });
                        readerSuggestions.style.display = 'block';
                    } else {
                        readerSuggestions.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('搜索读者失败:', error);
                    readerSuggestions.style.display = 'none';
                });
        }, 300);
    });

    // 图书搜索
    let bookSearchTimeout;
    isbnInput.addEventListener('input', function() {
        clearTimeout(bookSearchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            bookSuggestions.style.display = 'none';
            bookInfo.style.display = 'none';
            return;
        }

        bookSearchTimeout = setTimeout(() => {
            fetch(`{{ url_for('staff.search_book') }}?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    bookSuggestions.innerHTML = '';
                    if (data.length > 0) {
                        data.forEach(book => {
                            const item = document.createElement('a');
                            item.className = 'list-group-item list-group-item-action';
                            item.href = '#';
                            item.innerHTML = `
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${book.title}</h6>
                                    <small>可借 ${book.available_quantity} 本</small>
                                </div>
                                <p class="mb-1">${book.author} - ${book.publisher}</p>
                                <small>ISBN: ${book.isbn}</small>
                            `;
                            item.addEventListener('click', function(e) {
                                e.preventDefault();
                                isbnInput.value = book.isbn;
                                bookSuggestions.style.display = 'none';
                                showBookInfo(book);
                            });
                            bookSuggestions.appendChild(item);
                        });
                        bookSuggestions.style.display = 'block';
                    } else {
                        bookSuggestions.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('搜索图书失败:', error);
                    bookSuggestions.style.display = 'none';
                });
        }, 300);
    });

    // 显示读者信息
    function showReaderInfo(reader) {
        readerDetails.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>姓名：</strong>${reader.name}<br>
                    <strong>编号：</strong>${reader.reader_id}<br>
                    <strong>部门：</strong>${reader.department}
                </div>
                <div class="col-md-6">
                    <strong>类型：</strong>${reader.reader_type}<br>
                    <strong>已借图书：</strong>${reader.current_loans} 本
                </div>
            </div>
        `;
        readerInfo.style.display = 'block';
    }

    // 显示图书信息
    function showBookInfo(book) {
        bookDetails.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>书名：</strong>${book.title}<br>
                    <strong>作者：</strong>${book.author}<br>
                    <strong>出版社：</strong>${book.publisher}
                </div>
                <div class="col-md-6">
                    <strong>ISBN：</strong>${book.isbn}<br>
                    <strong>可借数量：</strong>${book.available_quantity} 本
                </div>
            </div>
        `;
        bookInfo.style.display = 'block';
    }

    // 点击其他地方隐藏建议列表
    document.addEventListener('click', function(e) {
        if (!readerInput.contains(e.target) && !readerSuggestions.contains(e.target)) {
            readerSuggestions.style.display = 'none';
        }
        if (!isbnInput.contains(e.target) && !bookSuggestions.contains(e.target)) {
            bookSuggestions.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
