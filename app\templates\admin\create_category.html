{% extends 'base.html' %}

{% block title %}添加图书类别 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- 添加图书类别卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">添加图书类别</h6>
                    <a href="{{ url_for('admin.categories') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('admin.create_category') }}" method="post">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="mb-3">
                            <label for="book_category_id" class="form-label">类别ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="book_category_id" name="book_category_id" required>
                            <div class="form-text">类别ID应为简短的字母和数字组合，如"CS"表示计算机科学类</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category_name" class="form-label">类别名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="category_name" name="category_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="fine_amount" class="form-label">罚款金额（元/天）</label>
                            <input type="number" class="form-control" id="fine_amount" name="fine_amount" min="0" step="0.01" value="0.1">
                            <div class="form-text">逾期每天的罚款金额，默认为0.1元/天</div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            // 验证类别ID格式
            const categoryIdInput = document.getElementById('book_category_id');
            const categoryIdValue = categoryIdInput.value.trim();
            if (!/^[A-Za-z0-9]{1,10}$/.test(categoryIdValue)) {
                alert('类别ID格式不正确，应为1-10位字母和数字组合');
                categoryIdInput.focus();
                event.preventDefault();
                return;
            }
            
            // 验证罚款金额
            const fineAmountInput = document.getElementById('fine_amount');
            const fineAmountValue = parseFloat(fineAmountInput.value);
            if (isNaN(fineAmountValue) || fineAmountValue < 0) {
                alert('罚款金额不能为负数');
                fineAmountInput.focus();
                event.preventDefault();
                return;
            }
        });
    });
</script>
{% endblock %}
