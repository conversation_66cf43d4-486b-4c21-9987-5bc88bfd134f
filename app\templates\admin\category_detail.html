{% extends 'base.html' %}

{% block title %}类别详情 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-4">
            <!-- 类别信息卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">类别信息</h6>
                    <div>
                        <a href="{{ url_for('admin.categories') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('admin.edit_category', category_id=category.book_category_id) }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-pencil"></i> 编辑
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 30%">类别ID</th>
                                    <td>{{ category.book_category_id }}</td>
                                </tr>
                                <tr>
                                    <th>类别名称</th>
                                    <td>{{ category.category_name }}</td>
                                </tr>
                                <tr>
                                    <th>罚款金额</th>
                                    <td>{{ category.fine_amount }} 元/天</td>
                                </tr>
                                <tr>
                                    <th>图书数量</th>
                                    <td>{{ books|length }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 删除类别按钮 -->
                    <div class="mt-4 text-end">
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteCategoryModal">
                            <i class="bi bi-trash"></i> 删除类别
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计信息卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">统计信息</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h5 class="small font-weight-bold">总数量</h5>
                                <div class="h3 mb-0 font-weight-bold text-gray-800">
                                    {{ books|sum(attribute='quantity') }}
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h5 class="small font-weight-bold">可借数量</h5>
                                <div class="h3 mb-0 font-weight-bold text-gray-800">
                                    {{ books|sum(attribute='available_quantity') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <h5 class="small font-weight-bold">借出比例</h5>
                        <div class="progress mb-2">
                            {% set total = books|sum(attribute='quantity') %}
                            {% set available = books|sum(attribute='available_quantity') %}
                            {% set percent = 0 if total == 0 else ((total - available) / total * 100)|int %}
                            <div class="progress-bar bg-info" role="progressbar" style="width: {{ percent }}%" 
                                aria-valuenow="{{ percent }}" aria-valuemin="0" aria-valuemax="100">
                                {{ percent }}%
                            </div>
                        </div>
                        <div class="small text-muted">
                            已借出 {{ total - available }} 本，共 {{ total }} 本
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- 该类别下的图书列表 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">该类别下的图书</h6>
                    <a href="{{ url_for('admin.create_book') }}" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus-circle"></i> 添加图书
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if books %}
                    <div class="table-responsive">
                        <table class="table table-admin table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>ISBN</th>
                                    <th>书名</th>
                                    <th>作者</th>
                                    <th>出版社</th>
                                    <th>出版年份</th>
                                    <th>总数量</th>
                                    <th>可借数量</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for book in books %}
                                <tr>
                                    <td>
                                        <span class="text-monospace">{{ book.isbn }}</span>
                                    </td>
                                    <td>{{ book.title }}</td>
                                    <td>{{ book.author }}</td>
                                    <td>{{ book.publisher }}</td>
                                    <td>{{ book.year }}</td>
                                    <td>{{ book.quantity }}</td>
                                    <td>
                                        {% if book.available_quantity > 0 %}
                                        <span class="badge bg-success">{{ book.available_quantity }}</span>
                                        {% else %}
                                        <span class="badge bg-danger">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('admin.book_detail', isbn=book.isbn) }}"
                                               class="btn btn-sm btn-outline-info"
                                               data-bs-toggle="tooltip"
                                               title="查看详情">
                                                <i class="bi bi-info-circle"></i>
                                            </a>
                                            <a href="{{ url_for('admin.edit_book', isbn=book.isbn) }}"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip"
                                               title="编辑图书">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state p-5 text-center">
                        <i class="bi bi-book text-muted mb-3"></i>
                        <p class="mb-0">该类别下暂无图书</p>
                        <a href="{{ url_for('admin.create_book') }}" class="btn btn-primary mt-3">
                            <i class="bi bi-plus-circle"></i> 添加图书
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除类别确认模态框 -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCategoryModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除类别"{{ category.category_name }}"吗？此操作不可逆。</p>
                <p class="text-danger">注意：如果此类别下有图书，将无法删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('admin.delete_category', category_id=category.book_category_id) }}" method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
