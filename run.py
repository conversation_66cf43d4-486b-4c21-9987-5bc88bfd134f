#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用入口
"""

import os
from app import create_app, db
from app.models import Reader, ReaderType, Book, BookCategory, Loan
from flask_migrate import Migrate

# 创建应用实例
app = create_app(os.getenv('FLASK_CONFIG') or 'default')
migrate = Migrate(app, db)


@app.shell_context_processor
def make_shell_context():
    """为Flask shell提供上下文"""
    return dict(
        app=app,
        db=db,
        Reader=Reader,
        ReaderType=ReaderType,
        Book=Book,
        BookCategory=BookCategory,
        Loan=Loan
    )


if __name__ == '__main__':
    app.run(debug=True)
