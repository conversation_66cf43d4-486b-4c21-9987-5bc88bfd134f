/* 管理员仪表盘样式 */

/* 统计卡片 */
.dashboard-card {
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
    position: relative;
    background-color: white;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-body {
    padding: 1rem;
}

.dashboard-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-icon.primary {
    background: linear-gradient(135deg, #1a3a5f, #4299e1);
}

.dashboard-card .card-icon.success {
    background: linear-gradient(135deg, #1e6641, #48bb78);
}

.dashboard-card .card-icon.info {
    background: linear-gradient(135deg, #1a4d8c, #63b3ed);
}

.dashboard-card .card-icon.warning {
    background: linear-gradient(135deg, #974c10, #ed8936);
}

.dashboard-card .dashboard-stat {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1.2;
    color: #2d3748;
}

.dashboard-card .card-title {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    color: #718096;
}

.dashboard-card .stat-trend {
    font-size: 0.8rem;
    margin-top: 0.5rem;
    font-weight: 500;
}

.dashboard-card .card-progress {
    height: 4px;
    background-color: #f1f1f1;
    overflow: hidden;
}

.dashboard-card .card-progress .progress-bar {
    height: 100%;
}

/* 图表卡片 */
.chart-card {
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    background-color: white;
    overflow: hidden;
    height: 100%;
    transition: all 0.3s ease;
}

.chart-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chart-card .card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-card .card-title {
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    color: #2d3748;
    font-size: 0.9rem;
}

.chart-card .card-title i {
    margin-right: 0.5rem;
    color: #4299e1;
    font-size: 1rem;
}

.chart-card .card-body {
    padding: 1rem;
}

.chart-card .chart-body {
    background-color: #f8fafc;
    border-radius: 0 0 1rem 1rem;
}

.chart-container {
    position: relative;
    height: 220px;
    width: 100%;
}

/* 图表信息 */
.chart-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-summary .summary-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1.2;
}

.chart-summary .summary-label {
    font-size: 0.8rem;
    color: #718096;
    font-weight: 500;
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
}

.chart-legend .legend-item {
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
    margin-bottom: 0.5rem;
}

.chart-legend .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.chart-legend .legend-text {
    font-size: 0.875rem;
    color: #718096;
}

/* 图表周期选择器 */
.chart-period-selector .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
}

.chart-refresh-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 表格样式 */
.table-admin {
    margin-bottom: 0;
}

.table-admin thead th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.7rem;
    letter-spacing: 0.05rem;
    padding: 0.6rem 1rem;
    border-top: none;
    border-bottom-width: 1px;
    color: #4a5568;
    position: relative;
}

.table-admin thead th.sortable {
    cursor: pointer;
}

.table-admin thead th.sortable:hover {
    background-color: #edf2f7;
}

.table-admin thead th.sortable i {
    font-size: 0.7rem;
    opacity: 0.5;
}

.table-admin thead th.sortable.sort-asc i:before {
    content: "\F12D";
}

.table-admin thead th.sortable.sort-desc i:before {
    content: "\F124";
}

.table-admin tbody td {
    padding: 0.6rem 1rem;
    vertical-align: middle;
    border-color: rgba(0, 0, 0, 0.05);
}

.table-admin.table-hover tbody tr:hover {
    background-color: rgba(66, 153, 225, 0.05);
}

/* 表格工具栏 */
.table-toolbar {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.table-toolbar .form-control {
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
}

.table-toolbar .input-group-text {
    padding: 0.375rem 0.75rem;
}

/* 表格分页 */
.table-pagination {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}

.pagination-info {
    color: #718096;
    font-size: 0.875rem;
}

.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #4299e1;
    background-color: #fff;
    border: 1px solid #e2e8f0;
}

.pagination .page-item.active .page-link {
    background-color: #4299e1;
    border-color: #4299e1;
}

.pagination .page-item.disabled .page-link {
    color: #a0aec0;
}

/* 状态徽章 */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 50rem;
    font-weight: 500;
    font-size: 0.7rem;
    display: inline-flex;
    align-items: center;
}

.badge-primary {
    background-color: rgba(66, 153, 225, 0.1);
    color: #4299e1;
}

.badge-success {
    background-color: rgba(72, 187, 120, 0.1);
    color: #48bb78;
}

.badge-warning {
    background-color: rgba(237, 137, 54, 0.1);
    color: #ed8936;
}

.badge-danger {
    background-color: rgba(245, 101, 101, 0.1);
    color: #f56565;
}

/* 头像 */
.avatar-xs {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

.text-monospace {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.875rem;
}

/* 动画效果 */
.slide-in-up {
    animation: slideInUp 0.5s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.zoom-in {
    animation: zoomIn 0.5s ease-out forwards;
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 优化仪表盘布局 */
.dashboard-optimized .chart-container {
    height: 220px;
}

.dashboard-optimized .table-admin thead th {
    padding: 0.5rem 0.75rem;
}

.dashboard-optimized .table-admin tbody td {
    padding: 0.5rem 0.75rem;
}

.dashboard-optimized .card-header {
    padding: 0.6rem 0.75rem;
}

.dashboard-optimized .card-body {
    padding: 0.75rem;
}

/* 读者列表页面样式 */
.search-form .input-group-text {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    color: #4a5568;
}

.search-form .form-control {
    border-color: #e2e8f0;
    box-shadow: none;
}

.search-form .form-control:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
}

.search-form .form-select {
    border-color: #e2e8f0;
    box-shadow: none;
}

.search-form .form-select:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
}

.table-pagination {
    background-color: #f8fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.empty-state {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #a0aec0;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.btn-outline-info, .btn-outline-primary {
    transition: all 0.2s ease;
}

.btn-outline-info:hover {
    background-color: rgba(66, 153, 225, 0.1);
    color: #4299e1;
}

.btn-outline-primary:hover {
    background-color: rgba(66, 153, 225, 0.1);
    color: #4299e1;
}

.reader-row {
    transition: background-color 0.2s ease;
}

.reader-row:hover {
    background-color: rgba(66, 153, 225, 0.05);
}

/* 响应式调整 */
@media (max-width: 992px) {
    .chart-container {
        height: 200px;
    }

    .dashboard-card .dashboard-stat {
        font-size: 1.5rem;
    }

    .dashboard-card .card-icon {
        width: 45px;
        height: 45px;
        font-size: 1.5rem;
    }

    .table-pagination {
        flex-direction: column;
        gap: 1rem;
    }

    .pagination-info {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .chart-container {
        height: 180px;
    }

    .search-form .row {
        gap: 0.5rem;
    }
}
