{% extends "base.html" %}

{% block title %}个人资料 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">个人资料</h1>
            <p class="text-muted mb-0">查看和编辑个人信息</p>
        </div>
        <a href="{{ url_for('staff.dashboard') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>返回仪表盘
        </a>
    </div>

    <div class="row">
        <!-- 个人信息 -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-person me-2"></i>基本信息
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('staff.update_profile') }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="staff_id" class="form-label">工作证号</label>
                                <input type="text" class="form-control" id="staff_id" value="{{ current_user.staff_id }}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">姓名</label>
                                <input type="text" class="form-control" id="name" value="{{ current_user.name }}" readonly>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">部门</label>
                                <input type="text" class="form-control" id="department" value="{{ current_user.department or '未分配' }}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">职位</label>
                                <input type="text" class="form-control" id="position" value="{{ current_user.position or '未分配' }}" readonly>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">联系电话</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="{{ current_user.phone or '' }}" placeholder="请输入联系电话">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="hire_date" class="form-label">入职日期</label>
                                <input type="text" class="form-control" id="hire_date" value="{{ current_user.hire_date.strftime('%Y-%m-%d') if current_user.hire_date else '未知' }}" readonly>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">账户状态</label>
                                <input type="text" class="form-control" id="status" 
                                       value="{% if current_user.status == 'active' %}活跃{% else %}停用{% endif %}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="work_days" class="form-label">工作天数</label>
                                <input type="text" class="form-control" id="work_days" value="{{ current_user.get_work_days() }}天" readonly>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>更新资料
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 工作统计 -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="bi bi-graph-up me-2"></i>工作统计
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="h4 text-primary">{{ current_user.get_processed_loans_count() }}</div>
                        <small class="text-muted">总处理借阅数</small>
                    </div>
                    
                    {% set monthly_stats = current_user.get_monthly_stats() %}
                    <div class="text-center mb-3">
                        <div class="h4 text-success">{{ monthly_stats.loans_processed }}</div>
                        <small class="text-muted">本月处理数</small>
                    </div>

                    <div class="text-center">
                        <div class="h4 text-info">{{ current_user.get_work_days() }}</div>
                        <small class="text-muted">工作天数</small>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="bi bi-lightning-charge me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-warning">
                            <i class="bi bi-key me-2"></i>修改密码
                        </a>
                        <a href="{{ url_for('staff.loan_history') }}" class="btn btn-info">
                            <i class="bi bi-clock-history me-2"></i>我的处理记录
                        </a>
                        <a href="{{ url_for('staff.reports') }}" class="btn btn-secondary">
                            <i class="bi bi-graph-up me-2"></i>工作报表
                        </a>
                    </div>
                </div>
            </div>

            <!-- 账户信息 -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="bi bi-info-circle me-2"></i>账户信息
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <div class="mb-2">
                            <strong>创建时间：</strong><br>
                            {{ current_user.created_at.strftime('%Y-%m-%d %H:%M') if current_user.created_at else '未知' }}
                        </div>
                        <div>
                            <strong>最后更新：</strong><br>
                            {{ current_user.updated_at.strftime('%Y-%m-%d %H:%M') if current_user.updated_at else '未知' }}
                        </div>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
