<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="图书借阅管理系统 - 高效管理图书借阅流程">
    <meta name="author" content="图书管理系统开发团队">
    <title>{% block title %}图书借阅管理系统{% endblock %}</title>

    <!-- 全局样式 -->
    <style>
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
        }
    </style>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">

    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/bootstrap.min.css') }}">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap-icons/bootstrap-icons.css') }}">

    <!-- Animate.css -->
    <link rel="stylesheet" href="{{ url_for('static', filename='animate.css/animate.min.css') }}">

    <!-- 统一样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">

    <!-- 登录页面样式 -->
    {% if request.endpoint and 'login' in request.endpoint %}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/login.css') }}">
    {% endif %}

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if request.endpoint and 'login' in request.endpoint %}
    <!-- 登录页面布局 - 左右两栏 -->
    <div class="login-page">
        <!-- 左侧背景区域 (65%) -->
        <div class="login-bg">
            <div class="login-bg-overlay"></div>
            <img src="{{ url_for('static', filename='img/login-bg-vibrant.svg') }}" alt="图书借阅管理系统" class="login-bg-image">
        </div>

        <!-- 右侧登录区域 (35%) -->
        <div class="login-form-container">
            {% block login_content %}{% endblock %}
        </div>
    </div>
    {% elif request.endpoint and 'error' in request.endpoint %}
    <!-- 错误页面布局 -->
    <div class="error-page">
        {% block error_content %}{% endblock %}
    </div>
    {% else %}
    <!-- 主应用布局 -->
    <div class="d-flex">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            {% block sidebar %}
            <!-- 侧边栏品牌 -->
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="Logo" width="40" height="40">
                </div>
                <div class="sidebar-brand-text">图书借阅系统</div>
            </div>

            <!-- 分隔线 -->
            <hr class="sidebar-divider">

            {% if current_user.is_authenticated %}
                {% if current_user.get_id().startswith('admin_') %}
                <!-- 管理员侧边栏 -->

                <!-- 主导航 -->
                <div class="sidebar-heading">主菜单</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}" href="{{ url_for('admin.dashboard') }}">
                            <i class="bi bi-speedometer2"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                </ul>

                <!-- 分隔线 -->
                <hr class="sidebar-divider">

                <!-- 读者管理 -->
                <div class="sidebar-heading">读者管理</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.readers' %}active{% endif %}" href="{{ url_for('admin.readers') }}">
                            <i class="bi bi-people"></i>
                            <span>读者列表</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.create_reader' %}active{% endif %}" href="{{ url_for('admin.create_reader') }}">
                            <i class="bi bi-person-plus"></i>
                            <span>添加读者</span>
                        </a>
                    </li>
                </ul>

                <!-- 分隔线 -->
                <hr class="sidebar-divider">

                <!-- 图书管理 -->
                <div class="sidebar-heading">图书管理</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.books' %}active{% endif %}" href="{{ url_for('admin.books') }}">
                            <i class="bi bi-book"></i>
                            <span>图书列表</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.create_book' %}active{% endif %}" href="{{ url_for('admin.create_book') }}">
                            <i class="bi bi-plus-circle"></i>
                            <span>添加图书</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.categories' %}active{% endif %}" href="{{ url_for('admin.categories') }}">
                            <i class="bi bi-tags"></i>
                            <span>图书类别</span>
                        </a>
                    </li>
                </ul>

                <!-- 分隔线 -->
                <hr class="sidebar-divider">

                <!-- 借阅管理 -->
                <div class="sidebar-heading">借阅管理</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.loans' %}active{% endif %}" href="{{ url_for('admin.loans') }}">
                            <i class="bi bi-journal-text"></i>
                            <span>借阅记录</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.create_loan' %}active{% endif %}" href="{{ url_for('admin.create_loan') }}">
                            <i class="bi bi-journal-plus"></i>
                            <span>借书处理</span>
                        </a>
                    </li>
                </ul>

                <!-- 分隔线 -->
                <hr class="sidebar-divider">

                <!-- 工作人员管理 -->
                <div class="sidebar-heading">工作人员管理</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.staff_list' %}active{% endif %}" href="{{ url_for('admin.staff_list') }}">
                            <i class="bi bi-people-fill"></i>
                            <span>工作人员列表</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.staff_add' %}active{% endif %}" href="{{ url_for('admin.staff_add') }}">
                            <i class="bi bi-person-plus-fill"></i>
                            <span>添加工作人员</span>
                        </a>
                    </li>
                </ul>
                {% elif current_user.get_id().startswith('staff_') %}
                <!-- 工作人员侧边栏 -->

                <!-- 主导航 -->
                <div class="sidebar-heading">工作台</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'staff.dashboard' %}active{% endif %}" href="{{ url_for('staff.dashboard') }}">
                            <i class="bi bi-speedometer2"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                </ul>

                <!-- 借阅管理 -->
                <div class="sidebar-heading">借阅管理</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'staff.borrow_book' %}active{% endif %}" href="{{ url_for('staff.borrow_book') }}">
                            <i class="bi bi-plus-circle"></i>
                            <span>办理借书</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'staff.return_book' %}active{% endif %}" href="{{ url_for('staff.return_book') }}">
                            <i class="bi bi-arrow-return-left"></i>
                            <span>办理还书</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'staff.fine_management' %}active{% endif %}" href="{{ url_for('staff.fine_management') }}">
                            <i class="bi bi-currency-yen"></i>
                            <span>罚款管理</span>
                        </a>
                    </li>
                </ul>

                <!-- 记录查询 -->
                <div class="sidebar-heading">记录查询</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'staff.loan_history' %}active{% endif %}" href="{{ url_for('staff.loan_history') }}">
                            <i class="bi bi-clock-history"></i>
                            <span>借阅历史</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'staff.reports' %}active{% endif %}" href="{{ url_for('staff.reports') }}">
                            <i class="bi bi-graph-up"></i>
                            <span>工作报表</span>
                        </a>
                    </li>
                </ul>



                {% else %}
                <!-- 读者侧边栏 -->

                <!-- 主导航 -->
                <div class="sidebar-heading">主菜单</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'reader.dashboard' %}active{% endif %}" href="{{ url_for('reader.dashboard') }}">
                            <i class="bi bi-speedometer2"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'reader.profile' %}active{% endif %}" href="{{ url_for('reader.profile') }}">
                            <i class="bi bi-person-circle"></i>
                            <span>个人信息</span>
                        </a>
                    </li>
                </ul>

                <!-- 分隔线 -->
                <hr class="sidebar-divider">

                <!-- 图书服务 -->
                <div class="sidebar-heading">图书服务</div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'reader.books' %}active{% endif %}" href="{{ url_for('reader.books') }}">
                            <i class="bi bi-search"></i>
                            <span>图书检索</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'reader.loans' %}active{% endif %}" href="{{ url_for('reader.loans') }}">
                            <i class="bi bi-journal-bookmark"></i>
                            <span>借阅记录</span>
                        </a>
                    </li>


                </ul>

                <!-- 侧边栏底部 -->

                {% endif %}
            {% endif %}

            <!-- 分隔线 -->
            <hr class="sidebar-divider mt-auto">

            <!-- 系统设置 -->
            <div class="sidebar-heading">系统设置</div>

            <ul class="nav flex-column">
                {% if current_user.is_authenticated %}
                {% if not current_user.get_id().startswith('admin_') %}
                <!-- 只为读者显示修改密码选项 -->
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'auth.change_password' %}active{% endif %}" href="{{ url_for('auth.change_password') }}">
                        <i class="bi bi-key"></i>
                        <span>修改密码</span>
                    </a>
                </li>
                {% endif %}

                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('auth.logout') }}">
                        <i class="bi bi-box-arrow-right"></i>
                        <span>退出登录</span>
                    </a>
                </li>
                {% endif %}
            </ul>
            {% endblock %}
        </aside>

        <!-- 主内容区 -->
        <main class="main-content" id="main-content">
            <!-- 顶部导航栏 -->
            {% block navbar %}
            {% if current_user.is_authenticated and current_user.get_id().startswith('admin_') %}
            <!-- 管理员导航栏 -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm w-100 fixed-top">
                <div class="container-fluid px-0">
                    <!-- 侧边栏切换按钮 -->
                    <button class="navbar-toggler border-0" type="button" id="sidebarToggle">
                        <span class="navbar-toggler-icon"></span>
                    </button>

                    <!-- 页面标题 -->
                    <h1 class="h4 mb-0 text-gray-800 d-none d-lg-inline-block">
                        {% if request.endpoint == 'admin.dashboard' %}
                            管理员仪表盘
                        {% elif request.endpoint == 'admin.readers' %}
                            读者管理
                        {% elif request.endpoint == 'admin.reader_detail' %}
                            读者详情
                        {% elif request.endpoint == 'admin.create_reader' %}
                            添加读者
                        {% elif request.endpoint == 'admin.edit_reader' %}
                            编辑读者
                        {% elif request.endpoint == 'auth.change_password' %}
                            修改密码
                        {% else %}
                            图书借阅管理系统
                        {% endif %}
                    </h1>

                    <!-- 顶部导航栏右侧 -->
                    <ul class="navbar-nav ms-auto me-0 me-lg-0">
                        <!-- 用户信息 -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="d-none d-lg-inline me-1">{{ current_user.username }}</span>
                                <i class="bi bi-person-circle"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="bi bi-box-arrow-right me-2 text-gray-400"></i>
                                    退出登录
                                </a>
                            </div>
                        </li>
                    </ul>
                </div>
            </nav>
            {% elif current_user.is_authenticated and current_user.get_id().startswith('staff_') %}
            <!-- 工作人员导航栏 -->
            <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                <!-- 侧边栏切换按钮 -->
                <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle me-3">
                    <i class="bi bi-list"></i>
                </button>

                <!-- 面包屑导航 -->
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('staff.dashboard') }}">工作台</a>
                        </li>
                        {% block breadcrumb %}{% endblock %}
                    </ol>
                </nav>

                <!-- 顶部导航栏右侧 -->
                <ul class="navbar-nav ms-auto">
                    <!-- 快速操作 -->
                    <li class="nav-item dropdown no-arrow mx-1">
                        <a class="nav-link dropdown-toggle" href="#" id="quickActions" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-lightning-charge"></i>
                            <span class="d-none d-lg-inline">快速操作</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="quickActions">
                            <a class="dropdown-item" href="{{ url_for('staff.borrow_book') }}">
                                <i class="bi bi-plus-circle me-2 text-gray-400"></i>
                                办理借书
                            </a>
                            <a class="dropdown-item" href="{{ url_for('staff.return_book') }}">
                                <i class="bi bi-arrow-return-left me-2 text-gray-400"></i>
                                办理还书
                            </a>
                            <a class="dropdown-item" href="{{ url_for('staff.fine_management') }}">
                                <i class="bi bi-currency-yen me-2 text-gray-400"></i>
                                罚款管理
                            </a>
                        </div>
                    </li>

                    <!-- 用户信息 -->
                    <li class="nav-item dropdown no-arrow">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="me-2 d-none d-lg-inline text-gray-600 small">{{ current_user.name }}（工作人员）</span>
                            <i class="bi bi-person-circle"></i>
                        </a>
                        <!-- 下拉菜单 -->
                        <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="userDropdown">
                            <a class="dropdown-item" href="{{ url_for('staff.profile') }}">
                                <i class="bi bi-person me-2 text-gray-400"></i>
                                个人资料
                            </a>
                            <a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="bi bi-key me-2 text-gray-400"></i>
                                修改密码
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right me-2 text-gray-400"></i>
                                退出登录
                            </a>
                        </div>
                    </li>
                </ul>
            </nav>
            {% elif current_user.is_authenticated %}
            <!-- 读者导航栏 -->
            <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                <!-- 侧边栏切换按钮 -->
                <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle me-3">
                    <i class="bi bi-list"></i>
                </button>

                <!-- 页面标题 -->
                <div class="d-none d-sm-inline-block">
                    <h1 class="h3 mb-0 text-gray-800">
                        {% if request.endpoint == 'reader.dashboard' %}
                            仪表盘
                        {% elif request.endpoint == 'reader.profile' %}
                            个人信息
                        {% elif request.endpoint == 'reader.books' %}
                            图书检索
                        {% elif request.endpoint == 'reader.book_detail' %}
                            图书详情
                        {% elif request.endpoint == 'reader.loans' %}
                            借阅记录
                        {% elif request.endpoint == 'auth.change_password' %}
                            修改密码
                        {% else %}
                            图书借阅管理系统
                        {% endif %}
                    </h1>
                </div>

                <!-- 顶部导航栏右侧 -->
                <ul class="navbar-nav ms-auto">
                    <!-- 用户信息 -->
                    <li class="nav-item dropdown no-arrow">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="me-2 d-none d-lg-inline text-gray-600 small">{{ current_user.name }}</span>
                            <i class="bi bi-person-circle"></i>
                        </a>
                        <!-- 下拉菜单 -->
                        <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in" aria-labelledby="userDropdown">
                            <a class="dropdown-item" href="{{ url_for('reader.profile') }}">
                                <i class="bi bi-person me-2 text-gray-400"></i>
                                个人信息
                            </a>
                            <a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="bi bi-key me-2 text-gray-400"></i>
                                修改密码
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right me-2 text-gray-400"></i>
                                退出登录
                            </a>
                        </div>
                    </li>
                </ul>
            </nav>
            {% else %}
            <!-- 默认导航栏 -->
            <nav class="navbar navbar-expand-lg navbar-light">
                <button id="sidebarToggle" class="btn btn-link text-dark me-3">
                    <i class="bi bi-list fs-4"></i>
                </button>

                <h1 class="h5 mb-0 text-dark">{% block page_title %}仪表盘{% endblock %}</h1>

                <div class="ms-auto d-flex align-items-center">
                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar">
                                <i class="bi bi-person"></i>
                            </div>
                            <span class="d-none d-lg-inline ms-2">游客</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.login') }}">
                                    <i class="bi bi-box-arrow-in-right"></i> 登录
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
            {% endif %}
            {% endblock %}

            <!-- 内容区 -->
            <div class="content-wrapper">
                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </div>

            <!-- 页脚 -->
            <footer class="app-footer" id="app-footer">
                {% block footer %}
                <p class="mb-0">© {{ now.year }} 图书借阅管理系统 版权所有</p>
                {% endblock %}
            </footer>
        </main>
    </div>
    {% endif %}

    <!-- jQuery -->
    <script src="{{ url_for('static', filename='jquery/jquery.min.js') }}"></script>

    <!-- Bootstrap 5 JS -->
    <script src="{{ url_for('static', filename='bootstrap/bootstrap.bundle.min.js') }}"></script>

    <!-- 自定义脚本 -->
    <script>
        // 侧边栏切换
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const appFooter = document.getElementById('app-footer');
            const sidebarToggle = document.getElementById('sidebarToggle');

            // 只有当所有元素都存在时才执行侧边栏相关功能
            if (sidebar && mainContent && appFooter) {
                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', function() {
                        sidebar.classList.toggle('sidebar-collapsed');
                        mainContent.classList.toggle('main-content-expanded');
                        appFooter.classList.toggle('app-footer-expanded');
                    });
                }

                // 响应式侧边栏
                function checkWidth() {
                    if (window.innerWidth < 992) {
                        sidebar.classList.add('sidebar-collapsed');
                        mainContent.classList.add('main-content-expanded');
                        appFooter.classList.add('app-footer-expanded');
                    } else {
                        sidebar.classList.remove('sidebar-collapsed');
                        mainContent.classList.remove('main-content-expanded');
                        appFooter.classList.remove('app-footer-expanded');
                    }
                }

                // 初始检查
                checkWidth();

                // 窗口大小变化时检查
                window.addEventListener('resize', checkWidth);
            }

            // 自动隐藏消息提示
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
