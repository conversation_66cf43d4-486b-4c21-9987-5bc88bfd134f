{% extends 'base.html' %}

{% block title %}添加读者 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 添加读者卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">添加读者</h6>
                    <a href="{{ url_for('admin.readers') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('admin.create_reader') }}" method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="reader_id" class="form-label">读者编号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="reader_id" name="reader_id" required>
                                <div class="invalid-feedback">请输入读者编号</div>
                            </div>
                            <div class="col-md-6">
                                <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback">请输入姓名</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="reader_type" class="form-label">读者类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="reader_type" name="reader_type" required>
                                    <option value="">请选择读者类型</option>
                                    {% for type in reader_types %}
                                    <option value="{{ type.reader_type }}">{{ type.reader_type }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">请选择读者类型</div>
                            </div>
                            <div class="col-md-6">
                                <label for="department" class="form-label">学院/部门</label>
                                <input type="text" class="form-control" id="department" name="department">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">手机号码</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">电子邮箱</label>
                                <input type="email" class="form-control" id="email" name="email">
                                <div class="invalid-feedback">请输入有效的电子邮箱地址</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="pwd" class="form-label">密码</label>
                                <input type="password" class="form-control" id="pwd" name="pwd" placeholder="默认为123456">
                                <small class="text-muted">如不填写，将使用默认密码：123456</small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> 重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 表单验证
    (function() {
        'use strict';

        // 获取所有需要验证的表单
        var forms = document.querySelectorAll('.needs-validation');

        // 循环并阻止提交
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                form.classList.add('was-validated');
            }, false);
        });
    })();
</script>
{% endblock %}
