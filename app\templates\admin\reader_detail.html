{% extends 'base.html' %}

{% block title %}读者详情 - 图书借阅管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-font-size.css') }}">
{% endblock %}

<!-- Sidebar and navbar are now in base.html -->

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8">
            <!-- 读者信息卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">读者信息</h6>
                    <div>
                        <a href="{{ url_for('admin.readers') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('admin.edit_reader', reader_id=reader.reader_id) }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-pencil"></i> 编辑
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 20%">读者编号</th>
                                    <td>{{ reader.reader_id }}</td>
                                </tr>
                                <tr>
                                    <th>姓名</th>
                                    <td>{{ reader.name }}</td>
                                </tr>
                                <tr>
                                    <th>读者类型</th>
                                    <td>{{ reader.reader_type }}</td>
                                </tr>
                                <tr>
                                    <th>学院/部门</th>
                                    <td>{{ reader.department }}</td>
                                </tr>
                                <tr>
                                    <th>手机号码</th>
                                    <td>{{ reader.phone }}</td>
                                </tr>
                                <tr>
                                    <th>电子邮箱</th>
                                    <td>{{ reader.email }}</td>
                                </tr>
                                <tr>
                                    <th>注册日期</th>
                                    <td>{{ reader.registration_date|datetime('%Y-%m-%d') }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3">
                        <form action="{{ url_for('admin.reset_reader_password', reader_id=reader.reader_id) }}" method="post" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-warning" onclick="return confirm('确定要重置密码吗？')">
                                <i class="bi bi-key"></i> 重置密码
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 当前借阅卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">当前借阅</h6>
                </div>
                <div class="card-body">
                    {% if reader.get_current_loans() %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>图书名称</th>
                                    <th>ISBN</th>
                                    <th>借阅日期</th>
                                    <th>应还日期</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for loan in reader.get_current_loans() %}
                                <tr>
                                    <td>{{ loan.book.title }}</td>
                                    <td>{{ loan.isbn }}</td>
                                    <td>{{ loan.loan_date|datetime('%Y-%m-%d') }}</td>
                                    <td>{{ loan.due_date|datetime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if loan.is_overdue() %}
                                        <span class="badge bg-danger">已逾期 {{ loan.get_overdue_days() }} 天</span>
                                        {% else %}
                                        <span class="badge bg-success">正常</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-journal-text fa-4x text-gray-300 mb-3"></i>
                        <p class="text-gray-500">该读者当前没有借阅任何图书</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 借阅权限卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">借阅权限</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5 class="small font-weight-bold">最大可借数量 <span class="float-right">{{ reader.reader_type_info.max_books }}</span></h5>
                        <div class="progress mb-4">
                            {% set percent = (reader.get_current_loans()|length / reader.reader_type_info.max_books * 100)|int %}
                            <div class="progress-bar bg-info" role="progressbar" style="width: {{ percent }}%" aria-valuenow="{{ percent }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h5 class="small font-weight-bold">借阅期限 <span class="float-right">{{ reader.reader_type_info.borrow_duration }} 天</span></h5>
                    </div>
                    <div class="mb-4">
                        <h5 class="small font-weight-bold">最大续借次数 <span class="float-right">{{ reader.reader_type_info.max_renewals }} 次</span></h5>
                    </div>
                </div>
            </div>

            <!-- 借阅统计卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">借阅统计</h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <i class="bi bi-journal-text fa-2x text-gray-300"></i>
                        </div>
                        <div class="col">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ reader.get_current_loans()|length }}</div>
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">当前借阅</div>
                        </div>
                    </div>
                    <hr>
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <i class="bi bi-journal-check fa-2x text-gray-300"></i>
                        </div>
                        <div class="col">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ reader.get_loan_history()|length }}</div>
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">历史借阅</div>
                        </div>
                    </div>
                    <hr>
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                        <div class="col">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ reader.get_overdue_loans()|length }}</div>
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">逾期借阅</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
