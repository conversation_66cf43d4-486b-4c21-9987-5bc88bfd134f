#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用初始化
"""

import os
import logging
from flask import Flask, redirect, url_for, request, render_template
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect
from flask_marshmallow import Marshmallow
from flask_restful import Api

from .config import config

# 初始化扩展
db = SQLAlchemy()
login_manager = LoginManager()
csrf = CSRFProtect()
ma = Marshmallow()
rest_api = Api()

# 配置登录管理器
login_manager.login_view = 'auth.login'
login_manager.login_message = '请先登录以访问此页面'
login_manager.login_message_category = 'warning'


def create_app(config_name=None):
    """创建Flask应用实例"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')

    # 创建应用实例
    static_folder = os.path.abspath(os.path.join(os.path.dirname(__file__), 'static'))
    app = Flask(__name__, static_folder=static_folder, static_url_path='/static')

    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 设置默认时区为中国标准时间
    import pytz
    app.config['TIMEZONE'] = pytz.timezone('Asia/Shanghai')

    # 配置日志
    if not app.debug and not app.testing:
        app.logger.setLevel(logging.INFO)

    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    csrf.init_app(app)
    ma.init_app(app)

    # 注册蓝图
    from .views.auth import auth_bp
    app.register_blueprint(auth_bp)

    from .views.reader import reader_bp
    app.register_blueprint(reader_bp)

    from .views.admin import admin_bp
    app.register_blueprint(admin_bp)

    from .views.staff import staff_bp
    app.register_blueprint(staff_bp)

    # 初始化API
    rest_api.init_app(app)
    rest_api.decorators = [csrf.exempt]  # 为API请求豁免CSRF保护

    # 注册API资源
    register_api_resources(app)

    # 注册错误处理器
    register_error_handlers(app)

    # 注册上下文处理器
    register_context_processors(app)

    # 注册模板过滤器
    register_template_filters(app)

    # 注册命令
    register_commands(app)

    # 添加根路径路由
    @app.route('/')
    def index():
        """根路径路由，重定向到登录页面"""
        return redirect(url_for('auth.login'))

    return app


def register_error_handlers(app):
    """注册错误处理器"""
    @app.errorhandler(400)
    def bad_request(e):
        # 检查请求是否期望JSON响应
        if request.headers.get('Accept') == 'application/json':
            return {'error': '请求参数错误'}, 400
        # 否则返回HTML页面
        return render_template('errors/400.html'), 400

    @app.errorhandler(401)
    def unauthorized(e):
        if request.headers.get('Accept') == 'application/json':
            return {'error': '未授权访问'}, 401
        return render_template('errors/401.html'), 401

    @app.errorhandler(403)
    def forbidden(e):
        if request.headers.get('Accept') == 'application/json':
            return {'error': '禁止访问'}, 403
        return render_template('errors/403.html'), 403

    @app.errorhandler(404)
    def page_not_found(e):
        if request.headers.get('Accept') == 'application/json':
            return {'error': '资源不存在'}, 404
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        if request.headers.get('Accept') == 'application/json':
            return {'error': '服务器内部错误'}, 500
        return render_template('errors/500.html'), 500


def register_context_processors(app):
    """注册上下文处理器"""
    @app.context_processor
    def inject_config():
        from datetime import datetime
        import pytz
        now = datetime.now(pytz.timezone('Asia/Shanghai'))
        return {
            'app_name': app.config['APP_NAME'],
            'now': now
        }


def register_template_filters(app):
    """注册模板过滤器"""
    @app.template_filter('datetime')
    def format_datetime(value, format='%Y-%m-%d %H:%M:%S'):
        """格式化日期时间"""
        if value is None:
            return ''

        # 处理日期对象
        import datetime
        if isinstance(value, datetime.date) and not isinstance(value, datetime.datetime):
            return value.strftime(format)

        # 确保时区为中国标准时间
        if value.tzinfo is None:
            value = app.config['TIMEZONE'].localize(value)
        return value.strftime(format)


def register_api_resources(app):
    """注册API资源"""
    # 导入API资源
    from .api.reader_api import ReaderResource, ReaderListResource
    from .api.book_api import BookResource, BookListResource, BookSearchResource
    from .api.loan_api import LoanResource, LoanListResource, LoanReturnResource, LoanRenewResource

    # 检查是否已经注册过API资源
    if 'api_readers' not in app.view_functions:
        # 读者API
        rest_api.add_resource(ReaderListResource, '/api/readers', endpoint='api_readers')
        rest_api.add_resource(ReaderResource, '/api/readers/<string:reader_id>', endpoint='api_reader')

        # 图书API
        rest_api.add_resource(BookListResource, '/api/books', endpoint='api_books')
        rest_api.add_resource(BookResource, '/api/books/<string:isbn>', endpoint='api_book')
        rest_api.add_resource(BookSearchResource, '/api/books/search', endpoint='api_book_search')

        # 借阅API
        rest_api.add_resource(LoanListResource, '/api/loans', endpoint='api_loans')
        rest_api.add_resource(LoanResource, '/api/loans/<int:loan_id>', endpoint='api_loan')
        rest_api.add_resource(LoanReturnResource, '/api/loans/<int:loan_id>/return', endpoint='api_loan_return')
        rest_api.add_resource(LoanRenewResource, '/api/loans/<int:loan_id>/renew', endpoint='api_loan_renew')


def register_commands(app):
    """注册自定义命令"""
    @app.cli.command('init-db')
    def init_db():
        """初始化数据库"""
        db.create_all()
        print('数据库初始化完成')
